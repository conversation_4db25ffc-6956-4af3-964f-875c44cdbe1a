# 数据库合并详细报告

**合并时间**: 2025-07-22 08:40:18  
**操作人员**: 数据库合并工具  

## 1. 原始数据库文件分析

### 发现的数据库文件
| 文件名 | 大小 | 记录数 | 最后修改时间 |
|--------|------|--------|--------------|
| stock_analysis-nsa.db | 1.77 MB | 1,824 条 | 2025/7/22 7:32:46 |
| stock_analysis.db | 1.19 MB | 1,194 条 | 2025/7/21 18:53:39 |
| stock_analysis联想.db | 0.69 MB | 651 条 | 2025/7/22 7:33:28 |
| **总计** | **3.65 MB** | **3,669 条** | - |

### 表结构分析
所有数据库都包含相同的表结构：

**表名**: `stock_analysis_results`

**字段结构**:
- `id` (INTEGER, 主键, 自增)
- `analysis_datetime` (TEXT, 非空) - 分析时间
- `stock_name` (TEXT, 非空) - 股票名称
- `stock_code` (TEXT, 非空) - 股票代码
- `value_score` (REAL, 非空) - 价值评分
- `growth_score` (REAL, 非空) - 成长评分
- `technical_score` (REAL, 非空) - 技术评分
- `total_score` (REAL, 非空) - 总评分
- `confidence_level` (TEXT, 非空) - 置信度等级
- `weight_config` (TEXT, 非空) - 权重配置
- `market_environment` (TEXT, 非空) - 市场环境
- `detailed_analysis` (TEXT) - 详细分析
- `investment_advice` (TEXT) - 投资建议
- `risk_warnings` (TEXT) - 风险警告
- `created_at` (TIMESTAMP, 默认当前时间) - 创建时间

## 2. 合并策略

### 去重策略
基于以下关键业务字段进行去重：
- `analysis_datetime` - 分析时间
- `stock_name` - 股票名称
- `stock_code` - 股票代码
- `value_score` - 价值评分
- `growth_score` - 成长评分
- `technical_score` - 技术评分
- `total_score` - 总评分
- `confidence_level` - 置信度等级
- `weight_config` - 权重配置
- `market_environment` - 市场环境

### 合并顺序
1. stock_analysis-nsa.db (作为基础数据)
2. stock_analysis.db (合并时去重)
3. stock_analysis联想.db (合并时去重)

## 3. 合并结果统计

### 总体统计
| 指标 | 数值 |
|------|------|
| 原始总记录数 | 3,669 条 |
| 合并后记录数 | 3,230 条 |
| 去除重复记录 | 439 条 |
| 去重率 | 11.96% |

### 各数据库合并详情
| 数据库文件 | 原始记录 | 插入记录 | 重复记录 | 重复率 |
|------------|----------|----------|----------|---------|
| stock_analysis-nsa.db | 1,824 | 1,824 | 0 | 0% |
| stock_analysis.db | 1,194 | 1,094 | 100 | 8.38% |
| stock_analysis联想.db | 651 | 312 | 339 | 52.07% |

## 4. 数据质量验证

### 基本统计信息
- **总记录数**: 3,230 条
- **唯一股票代码数**: 3,205 个
- **时间跨度**: 2025-01-19 14:30:00 至 2025-07-22 07:45:00
- **数据完整性**: ✅ 无数据冲突或异常

### 分析次数最多的股票
| 股票代码 | 分析次数 |
|----------|----------|
| SZ300750 | 4 次 |
| SZ300591 | 2 次 |
| SZ300491 | 2 次 |
| SZ300460 | 2 次 |
| SZ002862 | 2 次 |
| SZ002792 | 2 次 |
| SZ000858 | 2 次 |
| SZ000692 | 2 次 |
| SZ000685 | 2 次 |
| SZ000683 | 2 次 |

### 置信度分布
| 置信度等级 | 记录数 | 占比 |
|------------|--------|------|
| 中 | 1,157 | 35.82% |
| 高 | 728 | 22.54% |
| Medium | 710 | 21.98% |
| High | 243 | 7.52% |
| 低 | 247 | 7.65% |
| Low | 141 | 4.37% |
| 谨慎考虑 | 3 | 0.09% |
| 强烈不推荐 | 1 | 0.03% |

## 5. 生成的文件

### 主要输出文件
1. **merged_stock_analysis.db** - 合并后的统一数据库文件
2. **merge_report_20250722_084018.json** - 详细的合并报告(JSON格式)
3. **database_analysis_report.json** - 原始数据库分析报告
4. **数据库合并详细报告.md** - 本报告文件

### 工具脚本
1. **database_analyzer.py** - 数据库结构分析工具
2. **database_merger.py** - 数据库合并工具

## 6. 数据冲突和异常情况

### 冲突处理
- ✅ **无数据冲突**: 所有数据库表结构完全一致
- ✅ **无插入错误**: 所有记录成功插入目标数据库
- ✅ **无数据损坏**: 验证显示数据完整性良好

### 去重效果分析
1. **stock_analysis-nsa.db**: 作为基础数据，无重复
2. **stock_analysis.db**: 与基础数据有8.38%的重复率，说明有部分数据重叠
3. **stock_analysis联想.db**: 与前两个数据库有52.07%的重复率，重复度较高

## 7. 建议和后续操作

### 数据使用建议
1. **主数据库**: 使用 `merged_stock_analysis.db` 作为统一的数据源
2. **备份策略**: 原始数据库文件已保留，建议定期备份合并后的数据库
3. **数据更新**: 后续新数据应直接添加到合并后的数据库中

### 性能优化建议
1. 考虑在 `stock_code` 和 `analysis_datetime` 字段上创建索引
2. 定期清理过期的分析数据
3. 考虑按时间分区存储历史数据

### 数据质量监控
1. 定期检查重复记录
2. 监控数据增长趋势
3. 验证分析结果的一致性

## 8. 技术细节

### 去重算法
使用MD5哈希算法对关键业务字段组合进行去重，确保相同的分析结果不会重复存储。

### 合并流程
1. 分析所有源数据库的表结构
2. 创建目标数据库并建立表结构
3. 按顺序处理每个源数据库
4. 对每条记录计算业务哈希值
5. 跳过已存在的重复记录
6. 验证合并结果的完整性

---

**合并操作成功完成** ✅  
**数据完整性验证通过** ✅  
**无数据丢失或损坏** ✅
