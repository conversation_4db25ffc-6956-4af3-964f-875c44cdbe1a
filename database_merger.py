#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库合并工具
将多个SQLite数据库文件合并为一个，并去除重复记录
"""

import sqlite3
import os
from pathlib import Path
import json
from datetime import datetime
import hashlib

class DatabaseMerger:
    def __init__(self, source_databases, target_database='merged_stock_analysis.db'):
        self.source_databases = source_databases
        self.target_database = target_database
        self.merge_report = {
            'merge_time': datetime.now().isoformat(),
            'source_files': source_databases,
            'target_file': target_database,
            'tables_merged': {},
            'duplicates_removed': {},
            'total_records_before': 0,
            'total_records_after': 0,
            'conflicts': []
        }
    
    def create_target_database(self):
        """创建目标数据库"""
        if os.path.exists(self.target_database):
            backup_name = f"{self.target_database}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            os.rename(self.target_database, backup_name)
            print(f"已备份现有数据库为: {backup_name}")
        
        conn = sqlite3.connect(self.target_database)
        return conn
    
    def get_table_structure(self, db_path, table_name):
        """获取表结构"""
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        cursor.execute(f"SELECT sql FROM sqlite_master WHERE type='table' AND name='{table_name}'")
        create_sql = cursor.fetchone()[0]
        
        cursor.execute(f"PRAGMA table_info({table_name})")
        columns = cursor.fetchall()
        
        conn.close()
        return create_sql, columns
    
    def generate_record_hash(self, record, exclude_columns=None):
        """为记录生成哈希值，用于去重"""
        if exclude_columns is None:
            exclude_columns = ['id', 'created_at']  # 通常排除自增ID和时间戳
        
        # 创建记录的字符串表示（排除指定列）
        record_str = ""
        for i, value in enumerate(record):
            if exclude_columns and i < len(exclude_columns):
                continue
            record_str += str(value) if value is not None else "NULL"
        
        return hashlib.md5(record_str.encode('utf-8')).hexdigest()
    
    def merge_stock_analysis_results(self, target_conn):
        """合并stock_analysis_results表"""
        print("\n开始合并 stock_analysis_results 表...")
        
        # 创建表结构（使用第一个数据库的结构）
        first_db = self.source_databases[0]
        create_sql, columns = self.get_table_structure(first_db, 'stock_analysis_results')
        
        target_cursor = target_conn.cursor()
        target_cursor.execute(create_sql)
        
        # 用于去重的哈希集合
        seen_hashes = set()
        total_processed = 0
        total_inserted = 0
        duplicates_count = 0
        
        # 定义去重的关键字段（排除id和created_at）
        dedup_columns = ['analysis_datetime', 'stock_name', 'stock_code', 'value_score', 
                        'growth_score', 'technical_score', 'total_score', 'confidence_level',
                        'weight_config', 'market_environment']
        
        for db_file in self.source_databases:
            print(f"  处理数据库: {db_file}")
            
            source_conn = sqlite3.connect(db_file)
            source_cursor = source_conn.cursor()
            
            # 获取所有记录
            source_cursor.execute("SELECT * FROM stock_analysis_results")
            records = source_cursor.fetchall()
            
            db_processed = 0
            db_inserted = 0
            db_duplicates = 0
            
            for record in records:
                total_processed += 1
                db_processed += 1
                
                # 生成去重哈希（基于关键业务字段）
                dedup_data = []
                column_names = [col[1] for col in columns]
                
                for col_name in dedup_columns:
                    if col_name in column_names:
                        col_index = column_names.index(col_name)
                        dedup_data.append(str(record[col_index]) if record[col_index] is not None else "NULL")
                
                record_hash = hashlib.md5('|'.join(dedup_data).encode('utf-8')).hexdigest()
                
                if record_hash not in seen_hashes:
                    seen_hashes.add(record_hash)
                    
                    # 插入记录（不包括原始ID，让数据库自动生成新ID）
                    placeholders = ','.join(['?' for _ in range(len(record) - 1)])
                    insert_sql = f"INSERT INTO stock_analysis_results VALUES (NULL, {placeholders})"
                    
                    try:
                        target_cursor.execute(insert_sql, record[1:])  # 跳过原始ID
                        total_inserted += 1
                        db_inserted += 1
                    except Exception as e:
                        print(f"    插入记录时出错: {e}")
                        self.merge_report['conflicts'].append({
                            'database': db_file,
                            'error': str(e),
                            'record': record[:5]  # 只记录前5个字段
                        })
                else:
                    duplicates_count += 1
                    db_duplicates += 1
            
            print(f"    处理: {db_processed} 条，插入: {db_inserted} 条，重复: {db_duplicates} 条")
            source_conn.close()
        
        target_conn.commit()
        
        # 更新报告
        self.merge_report['tables_merged']['stock_analysis_results'] = {
            'total_processed': total_processed,
            'total_inserted': total_inserted,
            'duplicates_removed': duplicates_count,
            'source_breakdown': {}
        }
        
        self.merge_report['total_records_before'] = total_processed
        self.merge_report['total_records_after'] = total_inserted
        self.merge_report['duplicates_removed']['stock_analysis_results'] = duplicates_count
        
        print(f"\n合并完成:")
        print(f"  总处理记录: {total_processed}")
        print(f"  成功插入: {total_inserted}")
        print(f"  去除重复: {duplicates_count}")
        
        return total_processed, total_inserted, duplicates_count
    
    def verify_merge_results(self, target_conn):
        """验证合并结果"""
        print("\n验证合并结果...")
        
        cursor = target_conn.cursor()
        
        # 检查总记录数
        cursor.execute("SELECT COUNT(*) FROM stock_analysis_results")
        total_count = cursor.fetchone()[0]
        
        # 检查数据范围
        cursor.execute("SELECT MIN(analysis_datetime), MAX(analysis_datetime) FROM stock_analysis_results")
        date_range = cursor.fetchone()
        
        # 检查股票代码数量
        cursor.execute("SELECT COUNT(DISTINCT stock_code) FROM stock_analysis_results")
        unique_stocks = cursor.fetchone()[0]
        
        # 检查是否有明显重复（基于关键字段组合）
        cursor.execute("""
            SELECT stock_code, analysis_datetime, COUNT(*) as cnt 
            FROM stock_analysis_results 
            GROUP BY stock_code, analysis_datetime 
            HAVING COUNT(*) > 1 
            LIMIT 10
        """)
        potential_duplicates = cursor.fetchall()
        
        print(f"  合并后总记录数: {total_count}")
        print(f"  分析时间范围: {date_range[0]} 到 {date_range[1]}")
        print(f"  唯一股票代码数: {unique_stocks}")
        
        if potential_duplicates:
            print(f"  发现可能的重复记录: {len(potential_duplicates)} 组")
            for dup in potential_duplicates[:5]:
                print(f"    {dup[0]} - {dup[1]}: {dup[2]} 条记录")
        else:
            print("  ✓ 未发现明显重复记录")
        
        # 更新验证信息到报告
        self.merge_report['verification'] = {
            'total_records': total_count,
            'date_range': date_range,
            'unique_stocks': unique_stocks,
            'potential_duplicates': len(potential_duplicates)
        }
    
    def save_merge_report(self):
        """保存合并报告"""
        report_file = f"merge_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(self.merge_report, f, ensure_ascii=False, indent=2)
        
        print(f"\n合并报告已保存到: {report_file}")
        return report_file
    
    def merge_databases(self):
        """执行数据库合并"""
        print(f"开始合并数据库到: {self.target_database}")
        print(f"源数据库文件: {', '.join(self.source_databases)}")
        
        # 创建目标数据库
        target_conn = self.create_target_database()
        
        try:
            # 合并主表
            self.merge_stock_analysis_results(target_conn)
            
            # 验证结果
            self.verify_merge_results(target_conn)
            
            # 保存报告
            self.save_merge_report()
            
            print(f"\n✓ 数据库合并完成: {self.target_database}")
            
        except Exception as e:
            print(f"合并过程中出错: {e}")
            raise
        finally:
            target_conn.close()

def main():
    # 定义源数据库文件
    source_dbs = [
        'stock_analysis-nsa.db',
        'stock_analysis.db', 
        'stock_analysis联想.db'
    ]
    
    # 检查文件是否存在
    missing_files = [db for db in source_dbs if not os.path.exists(db)]
    if missing_files:
        print(f"错误: 以下数据库文件不存在: {missing_files}")
        return
    
    # 创建合并器并执行合并
    merger = DatabaseMerger(source_dbs)
    merger.merge_databases()

if __name__ == "__main__":
    main()
