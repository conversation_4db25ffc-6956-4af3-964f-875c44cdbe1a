#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强版未分析股票CSV生成器
尝试通过多种方式获取股票名称，提高CSV文件质量
"""

import json
import csv
import sqlite3
import re
from datetime import datetime

class EnhancedStockCSVGenerator:
    def __init__(self, json_report_file='股票分析完整性报告_20250722_094306.json', 
                 db_file='merged_stock_analysis.db',
                 csv_source='股票代码列表.csv',
                 output_file='未分析股票清单.csv'):
        self.json_report_file = json_report_file
        self.db_file = db_file
        self.csv_source = csv_source
        self.output_file = output_file
        self.missing_stocks = []
        self.stock_names = {}
        
    def load_missing_stocks(self):
        """从JSON报告中加载未分析股票列表"""
        try:
            with open(self.json_report_file, 'r', encoding='utf-8') as f:
                report = json.load(f)
            
            self.missing_stocks = report['missing_stocks']['list']
            print(f"从报告中加载了 {len(self.missing_stocks)} 只未分析股票")
            
        except Exception as e:
            print(f"加载JSON报告失败: {e}")
            raise
    
    def get_stock_names_from_csv(self):
        """从原始CSV文件中尝试获取股票名称"""
        try:
            encodings = ['utf-8', 'gbk', 'gb2312', 'utf-8-sig']
            
            for encoding in encodings:
                try:
                    with open(self.csv_source, 'r', encoding=encoding, newline='') as f:
                        reader = csv.reader(f)
                        rows = list(reader)
                        if rows:
                            headers = rows[0]
                            data_rows = rows[1:]
                    print(f"成功使用 {encoding} 编码读取原始CSV文件")
                    break
                except UnicodeDecodeError:
                    continue
            
            print(f"原始CSV列名: {headers}")
            
            # 查找股票名称列和股票代码列
            name_col_idx = None
            code_col_idx = None
            
            for i, col in enumerate(headers):
                col_lower = col.lower()
                if '名称' in col or 'name' in col_lower or '简称' in col:
                    name_col_idx = i
                elif 'code' in col_lower or '代码' in col or '股票代码' in col:
                    code_col_idx = i
            
            if name_col_idx is not None and code_col_idx is not None:
                print(f"找到名称列: {headers[name_col_idx]} (索引: {name_col_idx})")
                print(f"找到代码列: {headers[code_col_idx]} (索引: {code_col_idx})")
                
                for row in data_rows:
                    if len(row) > max(name_col_idx, code_col_idx):
                        raw_code = str(row[code_col_idx]).strip().upper()
                        raw_name = str(row[name_col_idx]).strip()
                        
                        # 标准化股票代码
                        standard_code = self.standardize_stock_code(raw_code)
                        if standard_code and raw_name:
                            self.stock_names[standard_code] = raw_name
                
                print(f"从原始CSV中获取了 {len(self.stock_names)} 个股票名称")
            else:
                print("未在原始CSV中找到名称和代码列")
                
        except Exception as e:
            print(f"从原始CSV获取股票名称失败: {e}")
    
    def standardize_stock_code(self, code):
        """标准化股票代码格式"""
        code = str(code).strip().upper()
        
        if '.' in code:
            # 格式如 000001.SZ
            parts = code.split('.')
            if len(parts) == 2:
                number, exchange = parts
                if exchange in ['SZ', 'SH']:
                    return f"{exchange}{number.zfill(6)}"
        elif len(code) == 6 and code.isdigit():
            # 纯数字代码，需要判断交易所
            if code.startswith(('000', '002', '300')):
                return f"SZ{code}"
            elif code.startswith(('600', '601', '603', '688')):
                return f"SH{code}"
        elif len(code) == 8 and code.startswith(('SZ', 'SH')):
            return code
        
        return None
    
    def get_stock_names_from_db(self):
        """从数据库中获取已知的股票名称"""
        try:
            conn = sqlite3.connect(self.db_file)
            cursor = conn.cursor()
            
            # 查询所有已分析股票的名称
            query = "SELECT DISTINCT stock_code, stock_name FROM stock_analysis_results"
            cursor.execute(query)
            
            db_names = {}
            for row in cursor:
                stock_code, stock_name = row
                db_names[stock_code] = stock_name
            
            conn.close()
            
            # 合并到主字典中（CSV优先）
            for code, name in db_names.items():
                if code not in self.stock_names:
                    self.stock_names[code] = name
            
            print(f"从数据库中补充了股票名称，总计: {len(self.stock_names)} 个")
            
        except Exception as e:
            print(f"从数据库获取股票名称失败: {e}")
    
    def generate_stock_name(self, stock_code):
        """为没有名称的股票生成描述性名称"""
        if stock_code.startswith('SH688'):
            return "科创板股票"
        elif stock_code.startswith('SZ301'):
            return "创业板注册制"
        elif stock_code.startswith('SZ300'):
            return "创业板股票"
        elif stock_code.startswith('SZ002'):
            return "中小板股票"
        elif stock_code.startswith('SZ000'):
            return "深市主板"
        elif stock_code.startswith('SH603'):
            return "沪市新股"
        elif stock_code.startswith('SH605'):
            return "沪市新股"
        elif stock_code.startswith('SH600'):
            return "沪市主板"
        else:
            return "待查询"
    
    def get_stock_name(self, stock_code):
        """获取股票名称"""
        if stock_code in self.stock_names:
            return self.stock_names[stock_code]
        else:
            return self.generate_stock_name(stock_code)
    
    def sort_stock_codes(self):
        """按要求排序股票代码：先SH后SZ，代码内按数字升序"""
        def sort_key(code):
            if code.startswith('SH'):
                exchange = 'A'  # SH排在前面
                number = code[2:]
            elif code.startswith('SZ'):
                exchange = 'B'  # SZ排在后面
                number = code[2:]
            else:
                exchange = 'Z'  # 其他排在最后
                number = code
            
            return (exchange, int(number) if number.isdigit() else float('inf'))
        
        self.missing_stocks.sort(key=sort_key)
        print(f"股票代码排序完成，共 {len(self.missing_stocks)} 只")
    
    def generate_csv(self):
        """生成CSV文件"""
        try:
            with open(self.output_file, 'w', newline='', encoding='utf-8-sig') as csvfile:
                writer = csv.writer(csvfile)
                
                # 写入表头
                writer.writerow(['序号', '股票名称', '股票代码'])
                
                # 写入数据
                for i, stock_code in enumerate(self.missing_stocks, 1):
                    stock_name = self.get_stock_name(stock_code)
                    writer.writerow([i, stock_name, stock_code])
                
            print(f"CSV文件生成成功: {self.output_file}")
            
        except Exception as e:
            print(f"生成CSV文件失败: {e}")
            raise
    
    def print_summary(self):
        """打印生成摘要"""
        print("\n" + "="*60)
        print("📊 增强版未分析股票清单CSV生成摘要")
        print("="*60)
        
        # 统计各交易所数量
        sh_count = sum(1 for code in self.missing_stocks if code.startswith('SH'))
        sz_count = sum(1 for code in self.missing_stocks if code.startswith('SZ'))
        
        # 统计名称类型
        real_names = sum(1 for code in self.missing_stocks if code in self.stock_names)
        generated_names = sum(1 for code in self.missing_stocks 
                            if code not in self.stock_names and self.get_stock_name(code) != "待查询")
        unknown_names = sum(1 for code in self.missing_stocks 
                          if self.get_stock_name(code) == "待查询")
        
        print(f"📈 总体统计:")
        print(f"  • 未分析股票总数: {len(self.missing_stocks):,} 只")
        print(f"  • 上交所(SH): {sh_count:,} 只 ({sh_count/len(self.missing_stocks)*100:.1f}%)")
        print(f"  • 深交所(SZ): {sz_count:,} 只 ({sz_count/len(self.missing_stocks)*100:.1f}%)")
        
        print(f"\n📝 名称获取情况:")
        print(f"  • 真实股票名称: {real_names:,} 只 ({real_names/len(self.missing_stocks)*100:.1f}%)")
        print(f"  • 生成描述名称: {generated_names:,} 只 ({generated_names/len(self.missing_stocks)*100:.1f}%)")
        print(f"  • 待查询名称: {unknown_names:,} 只 ({unknown_names/len(self.missing_stocks)*100:.1f}%)")
        
        print(f"\n📁 输出文件:")
        print(f"  • 文件名: {self.output_file}")
        print(f"  • 编码格式: UTF-8 with BOM")
        print(f"  • 列结构: 序号, 股票名称, 股票代码")
        print(f"  • 排序方式: SH在前，SZ在后，代码内按数字升序")
        
        # 显示各类型股票的示例
        print(f"\n📋 数据示例:")
        
        # 显示前10个
        print(f"  前10只股票:")
        for i in range(min(10, len(self.missing_stocks))):
            code = self.missing_stocks[i]
            name = self.get_stock_name(code)
            print(f"    {i+1:3d}. {name:<15} {code}")
        
        # 显示不同类型的示例
        print(f"\n  各板块示例:")
        examples = {}
        for code in self.missing_stocks:
            name = self.get_stock_name(code)
            if name not in examples and name != "待查询":
                examples[name] = code
                if len(examples) >= 8:
                    break
        
        for name, code in examples.items():
            print(f"    • {name:<15} {code}")
        
        print(f"\n✅ 增强版CSV文件生成完成！")
    
    def run(self):
        """执行完整的生成流程"""
        print("🚀 开始生成增强版未分析股票清单CSV文件...")
        
        # 1. 加载未分析股票列表
        self.load_missing_stocks()
        
        # 2. 从原始CSV获取股票名称
        self.get_stock_names_from_csv()
        
        # 3. 从数据库补充股票名称
        self.get_stock_names_from_db()
        
        # 4. 排序股票代码
        self.sort_stock_codes()
        
        # 5. 生成CSV文件
        self.generate_csv()
        
        # 6. 打印摘要
        self.print_summary()

def main():
    """主函数"""
    try:
        generator = EnhancedStockCSVGenerator()
        generator.run()
        
    except Exception as e:
        print(f"❌ 生成过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
