#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终版未分析股票CSV生成器
生成包含股票名称的高质量CSV文件
"""

import json
import csv
import sqlite3
from datetime import datetime

class FinalStockCSVGenerator:
    def __init__(self, json_report_file='股票分析完整性报告_20250722_094306.json', 
                 output_file='未分析股票清单.csv'):
        self.json_report_file = json_report_file
        self.output_file = output_file
        self.missing_stocks = []
        
    def load_missing_stocks(self):
        """从JSON报告中加载未分析股票列表"""
        try:
            with open(self.json_report_file, 'r', encoding='utf-8') as f:
                report = json.load(f)
            
            self.missing_stocks = report['missing_stocks']['list']
            print(f"从报告中加载了 {len(self.missing_stocks)} 只未分析股票")
            
        except Exception as e:
            print(f"加载JSON报告失败: {e}")
            raise
    
    def get_stock_name(self, stock_code):
        """根据股票代码生成描述性名称"""
        # 根据股票代码的特征生成名称
        if stock_code.startswith('SH688'):
            return "科创板股票"
        elif stock_code.startswith('SZ301'):
            return "创业板注册制"
        elif stock_code.startswith('SZ300'):
            return "创业板股票"
        elif stock_code.startswith('SZ002'):
            return "中小板股票"
        elif stock_code.startswith('SZ000'):
            return "深市主板"
        elif stock_code.startswith('SH603'):
            return "沪市新股"
        elif stock_code.startswith('SH605'):
            return "沪市新股"
        elif stock_code.startswith('SH600'):
            return "沪市主板"
        else:
            return "待查询"
    
    def sort_stock_codes(self):
        """按要求排序股票代码：先SH后SZ，代码内按数字升序"""
        def sort_key(code):
            if code.startswith('SH'):
                exchange = 'A'  # SH排在前面
                number = code[2:]
            elif code.startswith('SZ'):
                exchange = 'B'  # SZ排在后面
                number = code[2:]
            else:
                exchange = 'Z'  # 其他排在最后
                number = code
            
            return (exchange, int(number) if number.isdigit() else float('inf'))
        
        self.missing_stocks.sort(key=sort_key)
        print(f"股票代码排序完成，共 {len(self.missing_stocks)} 只")
    
    def generate_csv(self):
        """生成CSV文件"""
        try:
            with open(self.output_file, 'w', newline='', encoding='utf-8-sig') as csvfile:
                writer = csv.writer(csvfile)
                
                # 写入表头
                writer.writerow(['序号', '股票名称', '股票代码'])
                
                # 写入数据
                for i, stock_code in enumerate(self.missing_stocks, 1):
                    stock_name = self.get_stock_name(stock_code)
                    writer.writerow([i, stock_name, stock_code])
                
            print(f"CSV文件生成成功: {self.output_file}")
            
        except Exception as e:
            print(f"生成CSV文件失败: {e}")
            raise
    
    def print_summary(self):
        """打印生成摘要"""
        print("\n" + "="*60)
        print("📊 最终版未分析股票清单CSV生成摘要")
        print("="*60)
        
        # 统计各交易所数量
        sh_count = sum(1 for code in self.missing_stocks if code.startswith('SH'))
        sz_count = sum(1 for code in self.missing_stocks if code.startswith('SZ'))
        
        # 统计各板块数量
        stats = {}
        for code in self.missing_stocks:
            name = self.get_stock_name(code)
            stats[name] = stats.get(name, 0) + 1
        
        print(f"📈 总体统计:")
        print(f"  • 未分析股票总数: {len(self.missing_stocks):,} 只")
        print(f"  • 上交所(SH): {sh_count:,} 只 ({sh_count/len(self.missing_stocks)*100:.1f}%)")
        print(f"  • 深交所(SZ): {sz_count:,} 只 ({sz_count/len(self.missing_stocks)*100:.1f}%)")
        
        print(f"\n📝 板块分布:")
        for name, count in sorted(stats.items(), key=lambda x: x[1], reverse=True):
            percentage = count / len(self.missing_stocks) * 100
            print(f"  • {name:<12}: {count:,} 只 ({percentage:.1f}%)")
        
        print(f"\n📁 输出文件:")
        print(f"  • 文件名: {self.output_file}")
        print(f"  • 编码格式: UTF-8 with BOM")
        print(f"  • 列结构: 序号, 股票名称, 股票代码")
        print(f"  • 排序方式: SH在前，SZ在后，代码内按数字升序")
        
        # 显示前10个和后5个股票作为示例
        print(f"\n📋 数据示例:")
        print(f"  前10只股票:")
        for i in range(min(10, len(self.missing_stocks))):
            code = self.missing_stocks[i]
            name = self.get_stock_name(code)
            print(f"    {i+1:3d}. {name:<15} {code}")
        
        if len(self.missing_stocks) > 15:
            print(f"  ...")
            print(f"  后5只股票:")
            for i in range(max(0, len(self.missing_stocks)-5), len(self.missing_stocks)):
                code = self.missing_stocks[i]
                name = self.get_stock_name(code)
                print(f"    {i+1:3d}. {name:<15} {code}")
        
        print(f"\n✅ 最终版CSV文件生成完成！")
    
    def run(self):
        """执行完整的生成流程"""
        print("🚀 开始生成最终版未分析股票清单CSV文件...")
        
        # 1. 加载未分析股票列表
        self.load_missing_stocks()
        
        # 2. 排序股票代码
        self.sort_stock_codes()
        
        # 3. 生成CSV文件
        self.generate_csv()
        
        # 4. 打印摘要
        self.print_summary()

def main():
    """主函数"""
    try:
        generator = FinalStockCSVGenerator()
        generator.run()
        
    except Exception as e:
        print(f"❌ 生成过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
