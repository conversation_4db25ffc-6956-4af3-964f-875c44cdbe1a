#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
股票分析完整性检查工具
对比CSV股票代码列表与数据库中已分析的股票，找出未分析的股票代码
"""

import sqlite3
import csv
from datetime import datetime
import json

class StockAnalysisCompletionChecker:
    def __init__(self, csv_file='股票代码列表.csv', db_file='merged_stock_analysis.db'):
        self.csv_file = csv_file
        self.db_file = db_file
        self.csv_stocks = set()
        self.db_stocks = set()
        self.missing_stocks = set()
        self.analysis_report = {}
        
    def read_csv_stock_codes(self):
        """读取CSV文件中的股票代码"""
        try:
            print(f"正在读取CSV文件: {self.csv_file}")

            # 尝试不同的编码格式
            encodings = ['utf-8', 'gbk', 'gb2312', 'utf-8-sig']
            rows = []
            headers = []

            for encoding in encodings:
                try:
                    with open(self.csv_file, 'r', encoding=encoding, newline='') as f:
                        reader = csv.reader(f)
                        rows = list(reader)
                        if rows:
                            headers = rows[0]
                            rows = rows[1:]  # 去掉表头
                    print(f"成功使用 {encoding} 编码读取CSV文件")
                    break
                except UnicodeDecodeError:
                    continue
                except Exception as e:
                    print(f"使用 {encoding} 编码读取失败: {e}")
                    continue

            if not rows:
                raise Exception("无法使用常见编码格式读取CSV文件或文件为空")

            print(f"CSV文件列名: {headers}")
            print(f"CSV文件数据行数: {len(rows)}")

            # 查找包含股票代码的列索引
            stock_code_column_idx = None

            for i, col in enumerate(headers):
                col_lower = col.lower()
                if any(keyword in col_lower for keyword in ['code', '代码', 'symbol']) or '股票代码' in col:
                    stock_code_column_idx = i
                    print(f"找到股票代码列: {col} (索引: {i})")
                    break

            # 如果没找到明确的股票代码列，尝试查找最后一列（通常是股票代码）
            if stock_code_column_idx is None and len(headers) > 0:
                stock_code_column_idx = len(headers) - 1  # 使用最后一列
                print(f"未找到明确的股票代码列，使用最后一列: {headers[stock_code_column_idx]} (索引: {stock_code_column_idx})")

            if stock_code_column_idx is None:
                raise Exception("无法确定股票代码列")

            # 提取股票代码并标准化
            for row in rows:
                if len(row) > stock_code_column_idx:
                    code = str(row[stock_code_column_idx]).strip().upper()

                    # 清理和标准化股票代码
                    # 处理可能的格式：SZ000001, 000001.SZ, 000001等
                    if '.' in code:
                        # 格式如 000001.SZ
                        parts = code.split('.')
                        if len(parts) == 2:
                            number, exchange = parts
                            if exchange in ['SZ', 'SH']:
                                code = f"{exchange}{number.zfill(6)}"
                    elif len(code) == 6 and code.isdigit():
                        # 纯数字代码，需要判断交易所
                        if code.startswith(('000', '002', '300')):
                            code = f"SZ{code}"
                        elif code.startswith(('600', '601', '603', '688')):
                            code = f"SH{code}"
                    elif not code.startswith(('SZ', 'SH')) and len(code) > 6:
                        # 可能已经包含交易所前缀但格式不标准
                        continue

                    if len(code) == 8 and code.startswith(('SZ', 'SH')):
                        self.csv_stocks.add(code)

            print(f"从CSV文件中读取到 {len(self.csv_stocks)} 个有效股票代码")
            print(f"示例代码: {list(self.csv_stocks)[:5]}")

        except Exception as e:
            print(f"读取CSV文件时出错: {e}")
            raise
    
    def read_db_stock_codes(self):
        """读取数据库中已分析的股票代码"""
        try:
            print(f"正在连接数据库: {self.db_file}")
            conn = sqlite3.connect(self.db_file)
            
            # 查询所有已分析的股票代码
            query = "SELECT DISTINCT stock_code FROM stock_analysis_results"
            cursor = conn.execute(query)
            
            for row in cursor:
                stock_code = row[0].strip().upper()
                self.db_stocks.add(stock_code)
            
            conn.close()
            
            print(f"从数据库中读取到 {len(self.db_stocks)} 个已分析的股票代码")
            print(f"示例代码: {list(self.db_stocks)[:5]}")
            
        except Exception as e:
            print(f"读取数据库时出错: {e}")
            raise
    
    def find_missing_stocks(self):
        """找出未分析的股票代码"""
        self.missing_stocks = self.csv_stocks - self.db_stocks
        
        print(f"\n=== 分析结果 ===")
        print(f"CSV中总股票数: {len(self.csv_stocks)}")
        print(f"数据库中已分析股票数: {len(self.db_stocks)}")
        print(f"未分析股票数: {len(self.missing_stocks)}")
        
        if len(self.csv_stocks) > 0:
            completion_rate = (len(self.db_stocks) / len(self.csv_stocks)) * 100
            print(f"分析完成率: {completion_rate:.2f}%")
        else:
            completion_rate = 0
            print("分析完成率: 无法计算（CSV中无有效股票代码）")
        
        return completion_rate
    
    def analyze_missing_patterns(self):
        """分析未分析股票的模式"""
        if not self.missing_stocks:
            return {}
        
        patterns = {
            'SZ_stocks': [],
            'SH_stocks': [],
            'by_prefix': {}
        }
        
        for stock in self.missing_stocks:
            if stock.startswith('SZ'):
                patterns['SZ_stocks'].append(stock)
                prefix = stock[2:5]  # 取前3位数字
            elif stock.startswith('SH'):
                patterns['SH_stocks'].append(stock)
                prefix = stock[2:5]  # 取前3位数字
            else:
                continue
            
            if prefix not in patterns['by_prefix']:
                patterns['by_prefix'][prefix] = []
            patterns['by_prefix'][prefix].append(stock)
        
        return patterns
    
    def generate_report(self):
        """生成详细的分析报告"""
        completion_rate = self.find_missing_stocks()
        patterns = self.analyze_missing_patterns()
        
        # 对未分析股票进行排序
        missing_sorted = sorted(list(self.missing_stocks))
        
        self.analysis_report = {
            'analysis_time': datetime.now().isoformat(),
            'summary': {
                'total_stocks_in_csv': len(self.csv_stocks),
                'analyzed_stocks_in_db': len(self.db_stocks),
                'missing_stocks_count': len(self.missing_stocks),
                'completion_rate_percent': round(completion_rate, 2)
            },
            'missing_stocks': {
                'list': missing_sorted,
                'sz_count': len(patterns.get('SZ_stocks', [])),
                'sh_count': len(patterns.get('SH_stocks', [])),
                'by_exchange': {
                    'SZ': sorted(patterns.get('SZ_stocks', [])),
                    'SH': sorted(patterns.get('SH_stocks', []))
                }
            },
            'data_quality': {
                'csv_file': self.csv_file,
                'db_file': self.db_file,
                'csv_sample_codes': list(self.csv_stocks)[:10],
                'db_sample_codes': list(self.db_stocks)[:10]
            }
        }
        
        return self.analysis_report
    
    def save_report(self, filename=None):
        """保存分析报告"""
        if filename is None:
            filename = f"股票分析完整性报告_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(self.analysis_report, f, ensure_ascii=False, indent=2)
        
        print(f"\n分析报告已保存到: {filename}")
        return filename
    
    def print_detailed_report(self):
        """打印详细报告"""
        print("\n" + "="*60)
        print("📊 股票分析完整性检查报告")
        print("="*60)
        
        summary = self.analysis_report['summary']
        missing = self.analysis_report['missing_stocks']
        
        print(f"\n📈 总体统计:")
        print(f"  • CSV中总股票数量: {summary['total_stocks_in_csv']:,}")
        print(f"  • 已分析股票数量: {summary['analyzed_stocks_in_db']:,}")
        print(f"  • 未分析股票数量: {summary['missing_stocks_count']:,}")
        print(f"  • 分析完成率: {summary['completion_rate_percent']:.2f}%")
        
        if missing['list']:
            print(f"\n📋 未分析股票详情:")
            print(f"  • 深交所(SZ)股票: {missing['sz_count']} 只")
            print(f"  • 上交所(SH)股票: {missing['sh_count']} 只")
            
            print(f"\n📝 未分析股票代码列表:")
            
            # 按交易所分组显示
            if missing['by_exchange']['SZ']:
                print(f"\n  深交所(SZ) - {len(missing['by_exchange']['SZ'])}只:")
                for i, code in enumerate(missing['by_exchange']['SZ'], 1):
                    print(f"    {i:2d}. {code}")
            
            if missing['by_exchange']['SH']:
                print(f"\n  上交所(SH) - {len(missing['by_exchange']['SH'])}只:")
                for i, code in enumerate(missing['by_exchange']['SH'], 1):
                    print(f"    {i:2d}. {code}")
        else:
            print(f"\n✅ 所有股票都已完成分析！")
        
        print(f"\n📊 数据质量检查:")
        quality = self.analysis_report['data_quality']
        print(f"  • CSV文件: {quality['csv_file']}")
        print(f"  • 数据库文件: {quality['db_file']}")
        print(f"  • CSV示例代码: {', '.join(quality['csv_sample_codes'][:5])}")
        print(f"  • 数据库示例代码: {', '.join(quality['db_sample_codes'][:5])}")

def main():
    """主函数"""
    print("🔍 开始股票分析完整性检查...")
    
    try:
        # 创建检查器
        checker = StockAnalysisCompletionChecker()
        
        # 读取数据
        checker.read_csv_stock_codes()
        checker.read_db_stock_codes()
        
        # 生成报告
        checker.generate_report()
        
        # 显示详细报告
        checker.print_detailed_report()
        
        # 保存报告
        checker.save_report()
        
        print(f"\n✅ 分析完成！")
        
    except Exception as e:
        print(f"❌ 分析过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
