{"analysis_time": "2025-07-22T09:43:06.688596", "summary": {"total_stocks_in_csv": 4802, "analyzed_stocks_in_db": 3205, "missing_stocks_count": 1600, "completion_rate_percent": 66.74}, "missing_stocks": {"list": ["SH600252", "SH600354", "SH600359", "SH600361", "SH600362", "SH600386", "SH600389", "SH600396", "SH600398", "SH600488", "SH600587", "SH600588", "SH600589", "SH600590", "SH600640", "SH600642", "SH600643", "SH600648", "SH600649", "SH600653", "SH600654", "SH600655", "SH600658", "SH600661", "SH600662", "SH603053", "SH603055", "SH603058", "SH603060", "SH603067", "SH603068", "SH603069", "SH603071", "SH603077", "SH603078", "SH603079", "SH603080", "SH603081", "SH603083", "SH603087", "SH603089", "SH603093", "SH603096", "SH603097", "SH603098", "SH603101", "SH603102", "SH603103", "SH603108", "SH603112", "SH603115", "SH603118", "SH603119", "SH603121", "SH603122", "SH603126", "SH603127", "SH603128", "SH603129", "SH603132", "SH603137", "SH603138", "SH603139", "SH603150", "SH603155", "SH603156", "SH603158", "SH603160", "SH603162", "SH603163", "SH603165", "SH603167", "SH603168", "SH603171", "SH603176", "SH603178", "SH603179", "SH603181", "SH603182", "SH603183", "SH603186", "SH603187", "SH603191", "SH603192", "SH603196", "SH603197", "SH603206", "SH603208", "SH603209", "SH603211", "SH603214", "SH603215", "SH603216", "SH603218", "SH603221", "SH603222", "SH603225", "SH603226", "SH603228", "SH603230", "SH603232", "SH603233", "SH603236", "SH603238", "SH603239", "SH603258", "SH603259", "SH603260", "SH603269", "SH603277", "SH603278", "SH603279", "SH603280", "SH603281", "SH603286", "SH603288", "SH603289", "SH603290", "SH603296", "SH603297", "SH603298", "SH603300", "SH603301", "SH603305", "SH603306", "SH603307", "SH603313", "SH603316", "SH603318", "SH603321", "SH603326", "SH603331", "SH603337", "SH603338", "SH603339", "SH603348", "SH603353", "SH603355", "SH603356", "SH603357", "SH603358", "SH603360", "SH603365", "SH603366", "SH603368", "SH603380", "SH603385", "SH603387", "SH603393", "SH603399", "SH603408", "SH603439", "SH603444", "SH603458", "SH603501", "SH603506", "SH603507", "SH603511", "SH603515", "SH603516", "SH603520", "SH603528", "SH603529", "SH603533", "SH603535", "SH603556", "SH603566", "SH603567", "SH603577", "SH603579", "SH603583", "SH603587", "SH603589", "SH603600", "SH603601", "SH603605", "SH603611", "SH603613", "SH603615", "SH603617", "SH603618", "SH603619", "SH603628", "SH603629", "SH603630", "SH603636", "SH603639", "SH603648", "SH603655", "SH603656", "SH603657", "SH603659", "SH603660", "SH603662", "SH603669", "SH603676", "SH603678", "SH603679", "SH603681", "SH603683", "SH603685", "SH603687", "SH603689", "SH603693", "SH603697", "SH603716", "SH603718", "SH603726", "SH603727", "SH603738", "SH603739", "SH603755", "SH603757", "SH603759", "SH603773", "SH603776", "SH603777", "SH603778", "SH603787", "SH603799", "SH603800", "SH603803", "SH603808", "SH603809", "SH603811", "SH603817", "SH603818", "SH603822", "SH603823", "SH603825", "SH603826", "SH603829", "SH603833", "SH603836", "SH603855", "SH603858", "SH603860", "SH603861", "SH603863", "SH603866", "SH603867", "SH603871", "SH603878", "SH603879", "SH603880", "SH603881", "SH603885", "SH603887", "SH603888", "SH603889", "SH603890", "SH603893", "SH603899", "SH603900", "SH603901", "SH603903", "SH603908", "SH603915", "SH603916", "SH603917", "SH603919", "SH603920", "SH603928", "SH603931", "SH603936", "SH603937", "SH603955", "SH603958", "SH603966", "SH603967", "SH603969", "SH603970", "SH603976", "SH603977", "SH603978", "SH603980", "SH603982", "SH603983", "SH603992", "SH603993", "SH603995", "SH603998", "SH603999", "SH605005", "SH605009", "SH605011", "SH605016", "SH605020", "SH605056", "SH605058", "SH605068", "SH605069", "SH605077", "SH605080", "SH605088", "SH605089", "SH605098", "SH605100", "SH605108", "SH605111", "SH605118", "SH605122", "SH605123", "SH605136", "SH605151", "SH605155", "SH605158", "SH605162", "SH605167", "SH605169", "SH605177", "SH605178", "SH605179", "SH605180", "SH605188", "SH605196", "SH605208", "SH605258", "SH605268", "SH605277", "SH605286", "SH605288", "SH605303", "SH605318", "SH605319", "SH605333", "SH605337", "SH605339", "SH605368", "SH605369", "SH605376", "SH605377", "SH605388", "SH605398", "SH605399", "SH605499", "SH605500", "SH605507", "SH605567", "SH605588", "SH605589", "SH605599", "SH688001", "SH688004", "SH688006", "SH688008", "SH688009", "SH688011", "SH688012", "SH688020", "SH688022", "SH688025", "SH688026", "SH688027", "SH688028", "SH688029", "SH688031", "SH688041", "SH688046", "SH688049", "SH688050", "SH688056", "SH688058", "SH688059", "SH688061", "SH688063", "SH688065", "SH688067", "SH688068", "SH688071", "SH688072", "SH688073", "SH688076", "SH688077", "SH688080", "SH688083", "SH688084", "SH688088", "SH688089", "SH688090", "SH688092", "SH688097", "SH688098", "SH688099", "SH688102", "SH688103", "SH688105", "SH688109", "SH688110", "SH688114", "SH688117", "SH688118", "SH688121", "SH688122", "SH688126", "SH688127", "SH688128", "SH688130", "SH688131", "SH688133", "SH688135", "SH688138", "SH688139", "SH688141", "SH688150", "SH688151", "SH688153", "SH688157", "SH688159", "SH688160", "SH688165", "SH688166", "SH688167", "SH688172", "SH688177", "SH688180", "SH688182", "SH688183", "SH688187", "SH688188", "SH688189", "SH688191", "SH688192", "SH688195", "SH688196", "SH688200", "SH688203", "SH688206", "SH688207", "SH688209", "SH688212", "SH688215", "SH688216", "SH688218", "SH688221", "SH688222", "SH688223", "SH688227", "SH688229", "SH688232", "SH688235", "SH688236", "SH688238", "SH688244", "SH688246", "SH688248", "SH688249", "SH688253", "SH688255", "SH688257", "SH688258", "SH688259", "SH688260", "SH688262", "SH688265", "SH688266", "SH688268", "SH688269", "SH688276", "SH688278", "SH688281", "SH688282", "SH688283", "SH688286", "SH688289", "SH688290", "SH688292", "SH688295", "SH688296", "SH688297", "SH688302", "SH688303", "SH688306", "SH688308", "SH688311", "SH688312", "SH688314", "SH688315", "SH688318", "SH688319", "SH688323", "SH688328", "SH688331", "SH688334", "SH688335", "SH688337", "SH688345", "SH688347", "SH688348", "SH688353", "SH688355", "SH688362", "SH688363", "SH688366", "SH688368", "SH688370", "SH688371", "SH688373", "SH688375", "SH688377", "SH688379", "SH688380", "SH688383", "SH688386", "SH688388", "SH688389", "SH688390", "SH688395", "SH688396", "SH688400", "SH688403", "SH688408", "SH688428", "SH688429", "SH688432", "SH688433", "SH688435", "SH688439", "SH688443", "SH688456", "SH688459", "SH688475", "SH688478", "SH688479", "SH688484", "SH688496", "SH688498", "SH688499", "SH688500", "SH688502", "SH688505", "SH688506", "SH688509", "SH688512", "SH688515", "SH688519", "SH688526", "SH688529", "SH688531", "SH688535", "SH688538", "SH688543", "SH688550", "SH688553", "SH688557", "SH688558", "SH688559", "SH688560", "SH688565", "SH688566", "SH688571", "SH688576", "SH688577", "SH688586", "SH688588", "SH688589", "SH688590", "SH688591", "SH688592", "SH688593", "SH688596", "SH688597", "SH688600", "SH688601", "SH688610", "SH688611", "SH688616", "SH688617", "SH688623", "SH688625", "SH688629", "SH688631", "SH688633", "SH688636", "SH688638", "SH688639", "SH688651", "SH688655", "SH688656", "SH688658", "SH688660", "SH688661", "SH688663", "SH688665", "SH688667", "SH688677", "SH688680", "SH688683", "SH688686", "SH688687", "SH688696", "SH688699", "SH688701", "SH688707", "SH688718", "SH688722", "SH688728", "SH688733", "SH688739", "SH688766", "SH688767", "SH688772", "SH688776", "SH688777", "SH688786", "SH688787", "SH688799", "SH688800", "SZ000006", "SZ000009", "SZ000011", "SZ000012", "SZ000014", "SZ000016", "SZ000017", "SZ000019", "SZ000020", "SZ000021", "SZ000025", "SZ000027", "SZ000028", "SZ000029", "SZ000031", "SZ000032", "SZ000035", "SZ000036", "SZ000037", "SZ000045", "SZ000050", "SZ000055", "SZ000056", "SZ000058", "SZ000059", "SZ000061", "SZ000062", "SZ000063", "SZ000065", "SZ000066", "SZ000068", "SZ000069", "SZ000078", "SZ000089", "SZ000099", "SZ000151", "SZ000156", "SZ000159", "SZ000166", "SZ000338", "SZ000400", "SZ000401", "SZ000403", "SZ000408", "SZ000410", "SZ000415", "SZ000417", "SZ000420", "SZ000421", "SZ000422", "SZ000423", "SZ000428", "SZ000501", "SZ000503", "SZ000507", "SZ000509", "SZ000510", "SZ000513", "SZ000514", "SZ000516", "SZ000517", "SZ000519", "SZ000520", "SZ000528", "SZ000530", "SZ000531", "SZ000532", "SZ000536", "SZ000544", "SZ000548", "SZ000550", "SZ000551", "SZ000552", "SZ000554", "SZ000557", "SZ000560", "SZ000561", "SZ000565", "SZ000566", "SZ000567", "SZ000568", "SZ000571", "SZ000572", "SZ000576", "SZ000582", "SZ000586", "SZ000589", "SZ000590", "SZ000596", "SZ000597", "SZ000598", "SZ000603", "SZ000605", "SZ000733", "SZ000737", "SZ000739", "SZ000750", "SZ000917", "SZ000920", "SZ000921", "SZ000923", "SZ001267", "SZ001269", "SZ001283", "SZ001311", "SZ001872", "SZ001896", "SZ001914", "SZ001965", "SZ002344", "SZ002345", "SZ002346", "SZ002347", "SZ002350", "SZ002351", "SZ002353", "SZ002358", "SZ002360", "SZ002362", "SZ002364", "SZ002365", "SZ002366", "SZ002369", "SZ002371", "SZ002373", "SZ002376", "SZ002377", "SZ002381", "SZ002382", "SZ002383", "SZ002385", "SZ002392", "SZ002393", "SZ002395", "SZ002396", "SZ002397", "SZ002398", "SZ002399", "SZ002400", "SZ002402", "SZ002403", "SZ002404", "SZ002405", "SZ002407", "SZ002409", "SZ002412", "SZ002415", "SZ002416", "SZ002420", "SZ002421", "SZ002424", "SZ002427", "SZ002430", "SZ002431", "SZ002437", "SZ002438", "SZ002439", "SZ002445", "SZ002446", "SZ002448", "SZ002449", "SZ002451", "SZ002453", "SZ002454", "SZ002455", "SZ002456", "SZ002459", "SZ002461", "SZ002462", "SZ002463", "SZ002465", "SZ002468", "SZ002472", "SZ002474", "SZ002480", "SZ002481", "SZ002483", "SZ002484", "SZ002486", "SZ002487", "SZ002489", "SZ002493", "SZ002494", "SZ002497", "SZ002498", "SZ002501", "SZ002506", "SZ002507", "SZ002513", "SZ002516", "SZ002520", "SZ002522", "SZ002527", "SZ002534", "SZ002535", "SZ002536", "SZ002537", "SZ002538", "SZ002542", "SZ002544", "SZ002546", "SZ002547", "SZ002549", "SZ002550", "SZ002553", "SZ002557", "SZ002563", "SZ002567", "SZ002568", "SZ002570", "SZ002573", "SZ002574", "SZ002579", "SZ002580", "SZ002581", "SZ002583", "SZ002584", "SZ002585", "SZ002587", "SZ002588", "SZ002589", "SZ002593", "SZ002595", "SZ002596", "SZ002599", "SZ002605", "SZ002606", "SZ002607", "SZ002608", "SZ002609", "SZ002612", "SZ002613", "SZ002615", "SZ002622", "SZ002628", "SZ002633", "SZ002634", "SZ002638", "SZ002642", "SZ002645", "SZ002648", "SZ002649", "SZ002651", "SZ002652", "SZ002653", "SZ002659", "SZ002660", "SZ002663", "SZ002668", "SZ002670", "SZ002673", "SZ002674", "SZ002675", "SZ002677", "SZ002678", "SZ002685", "SZ002691", "SZ002695", "SZ002696", "SZ002698", "SZ002702", "SZ002703", "SZ002705", "SZ002714", "SZ002715", "SZ002716", "SZ002718", "SZ002721", "SZ002722", "SZ002724", "SZ002728", "SZ002730", "SZ002731", "SZ002732", "SZ002733", "SZ002734", "SZ002735", "SZ002736", "SZ002737", "SZ002745", "SZ002747", "SZ002748", "SZ002749", "SZ002752", "SZ002753", "SZ002756", "SZ002757", "SZ002758", "SZ002760", "SZ002761", "SZ002763", "SZ002765", "SZ002768", "SZ002769", "SZ002775", "SZ002778", "SZ002779", "SZ002782", "SZ002783", "SZ002787", "SZ002790", "SZ002791", "SZ002795", "SZ002799", "SZ002802", "SZ002803", "SZ002806", "SZ002807", "SZ002820", "SZ002821", "SZ002823", "SZ002824", "SZ002826", "SZ002828", "SZ002829", "SZ002830", "SZ002831", "SZ002832", "SZ002835", "SZ002836", "SZ002837", "SZ002840", "SZ002841", "SZ002843", "SZ002845", "SZ002846", "SZ002853", "SZ002855", "SZ002858", "SZ002859", "SZ002860", "SZ002861", "SZ002867", "SZ002873", "SZ002877", "SZ002880", "SZ002884", "SZ002886", "SZ002891", "SZ002892", "SZ002896", "SZ002897", "SZ002899", "SZ002900", "SZ002901", "SZ002906", "SZ002907", "SZ002908", "SZ002912", "SZ002913", "SZ002915", "SZ002916", "SZ002918", "SZ002919", "SZ002923", "SZ002926", "SZ002927", "SZ002928", "SZ002929", "SZ002931", "SZ002932", "SZ002933", "SZ002940", "SZ002941", "SZ002942", "SZ002945", "SZ002946", "SZ002949", "SZ002953", "SZ002956", "SZ002961", "SZ002963", "SZ002966", "SZ002968", "SZ002969", "SZ002971", "SZ002973", "SZ002975", "SZ002976", "SZ002980", "SZ002981", "SZ002982", "SZ002984", "SZ002985", "SZ002988", "SZ002989", "SZ002991", "SZ002997", "SZ002998", "SZ003000", "SZ003001", "SZ003002", "SZ003006", "SZ003007", "SZ003009", "SZ003011", "SZ003012", "SZ003013", "SZ003015", "SZ003016", "SZ003017", "SZ003026", "SZ003027", "SZ003028", "SZ003029", "SZ003035", "SZ003036", "SZ003040", "SZ003041", "SZ003042", "SZ003043", "SZ003816", "SZ300001", "SZ300004", "SZ300005", "SZ300006", "SZ300007", "SZ300010", "SZ300012", "SZ300013", "SZ300015", "SZ300016", "SZ300018", "SZ300021", "SZ300022", "SZ300024", "SZ300025", "SZ300027", "SZ300033", "SZ300037", "SZ300041", "SZ300042", "SZ300043", "SZ300045", "SZ300046", "SZ300048", "SZ300051", "SZ300056", "SZ300058", "SZ300061", "SZ300065", "SZ300068", "SZ300072", "SZ300074", "SZ300075", "SZ300077", "SZ300079", "SZ300080", "SZ300082", "SZ300083", "SZ300084", "SZ300087", "SZ300094", "SZ300095", "SZ300100", "SZ300101", "SZ300106", "SZ300109", "SZ300110", "SZ300111", "SZ300119", "SZ300122", "SZ300124", "SZ300129", "SZ300130", "SZ300131", "SZ300133", "SZ300134", "SZ300138", "SZ300139", "SZ300141", "SZ300142", "SZ300143", "SZ300145", "SZ300146", "SZ300151", "SZ300154", "SZ300157", "SZ300158", "SZ300161", "SZ300170", "SZ300179", "SZ300180", "SZ300182", "SZ300183", "SZ300184", "SZ300187", "SZ300188", "SZ300191", "SZ300201", "SZ300206", "SZ300207", "SZ300209", "SZ300210", "SZ300212", "SZ300214", "SZ300215", "SZ300223", "SZ300224", "SZ300229", "SZ300230", "SZ300232", "SZ300235", "SZ300238", "SZ300241", "SZ300243", "SZ300247", "SZ300248", "SZ300250", "SZ300253", "SZ300256", "SZ300259", "SZ300260", "SZ300270", "SZ300271", "SZ300275", "SZ300277", "SZ300278", "SZ300279", "SZ300283", "SZ300285", "SZ300288", "SZ300289", "SZ300290", "SZ300291", "SZ300292", "SZ300293", "SZ300294", "SZ300296", "SZ300302", "SZ300305", "SZ300307", "SZ300314", "SZ300315", "SZ300316", "SZ300317", "SZ300318", "SZ300320", "SZ300321", "SZ300323", "SZ300324", "SZ300329", "SZ300334", "SZ300339", "SZ300345", "SZ300346", "SZ300347", "SZ300350", "SZ300351", "SZ300352", "SZ300354", "SZ300355", "SZ300357", "SZ300358", "SZ300360", "SZ300363", "SZ300364", "SZ300366", "SZ300368", "SZ300371", "SZ300373", "SZ300381", "SZ300382", "SZ300393", "SZ300395", "SZ300400", "SZ300403", "SZ300404", "SZ300407", "SZ300410", "SZ300412", "SZ300414", "SZ300417", "SZ300418", "SZ300421", "SZ300422", "SZ300423", "SZ300425", "SZ300426", "SZ300430", "SZ300433", "SZ300437", "SZ300442", "SZ300444", "SZ300448", "SZ300449", "SZ300451", "SZ300454", "SZ300498", "SZ300500", "SZ300501", "SZ300502", "SZ300540", "SZ300542", "SZ300543", "SZ300546", "SZ300549", "SZ300550", "SZ300551", "SZ300556", "SZ300557", "SZ300558", "SZ300560", "SZ300566", "SZ300568", "SZ300573", "SZ300575", "SZ300578", "SZ300579", "SZ300580", "SZ300581", "SZ300582", "SZ300584", "SZ300588", "SZ300589", "SZ300592", "SZ300593", "SZ300594", "SZ300595", "SZ300596", "SZ300597", "SZ300598", "SZ300599", "SZ300600", "SZ300607", "SZ300609", "SZ300610", "SZ300614", "SZ300615", "SZ300622", "SZ300623", "SZ300626", "SZ300627", "SZ300629", "SZ300636", "SZ300637", "SZ300639", "SZ300641", "SZ300644", "SZ300645", "SZ300648", "SZ300649", "SZ300653", "SZ300655", "SZ300656", "SZ300659", "SZ300660", "SZ300661", "SZ300665", "SZ300666", "SZ300668", "SZ300669", "SZ300670", "SZ300672", "SZ300675", "SZ300676", "SZ300679", "SZ300681", "SZ300682", "SZ300683", "SZ300686", "SZ300688", "SZ300689", "SZ300690", "SZ300694", "SZ300696", "SZ300697", "SZ300701", "SZ300703", "SZ300712", "SZ300713", "SZ300715", "SZ300717", "SZ300720", "SZ300722", "SZ300724", "SZ300725", "SZ300727", "SZ300730", "SZ300731", "SZ300732", "SZ300733", "SZ300736", "SZ300739", "SZ300740", "SZ300741", "SZ300745", "SZ300746", "SZ300753", "SZ300755", "SZ300765", "SZ300766", "SZ300770", "SZ300771", "SZ300773", "SZ300777", "SZ300778", "SZ300781", "SZ300782", "SZ300786", "SZ300787", "SZ300788", "SZ300789", "SZ300792", "SZ300796", "SZ300800", "SZ300801", "SZ300803", "SZ300805", "SZ300806", "SZ300809", "SZ300812", "SZ300813", "SZ300814", "SZ300815", "SZ300817", "SZ300820", "SZ300822", "SZ300823", "SZ300824", "SZ300826", "SZ300829", "SZ300831", "SZ300832", "SZ300833", "SZ300834", "SZ300837", "SZ300838", "SZ300839", "SZ300845", "SZ300850", "SZ300852", "SZ300853", "SZ300854", "SZ300855", "SZ300857", "SZ300859", "SZ300860", "SZ300862", "SZ300863", "SZ300864", "SZ300865", "SZ300867", "SZ300868", "SZ300870", "SZ300871", "SZ300873", "SZ300875", "SZ300876", "SZ300879", "SZ300880", "SZ300884", "SZ300885", "SZ300886", "SZ300887", "SZ300888", "SZ300889", "SZ300892", "SZ300893", "SZ300894", "SZ300896", "SZ300898", "SZ300902", "SZ300904", "SZ300906", "SZ300909", "SZ300910", "SZ300912", "SZ300915", "SZ300916", "SZ300917", "SZ300918", "SZ300919", "SZ300920", "SZ300921", "SZ300925", "SZ300926", "SZ300927", "SZ300928", "SZ300929", "SZ300935", "SZ300937", "SZ300940", "SZ300941", "SZ300942", "SZ300943", "SZ300945", "SZ300948", "SZ300951", "SZ300955", "SZ300957", "SZ300961", "SZ300964", "SZ300967", "SZ300968", "SZ300969", "SZ300970", "SZ300971", "SZ300975", "SZ300976", "SZ300977", "SZ300979", "SZ300983", "SZ300988", "SZ300989", "SZ300990", "SZ300991", "SZ300992", "SZ300993", "SZ300996", "SZ300997", "SZ301001", "SZ301003", "SZ301004", "SZ301009", "SZ301010", "SZ301011", "SZ301012", "SZ301013", "SZ301015", "SZ301017", "SZ301018", "SZ301021", "SZ301022", "SZ301023", "SZ301025", "SZ301026", "SZ301027", "SZ301028", "SZ301032", "SZ301035", "SZ301036", "SZ301037", "SZ301038", "SZ301052", "SZ301056", "SZ301057", "SZ301058", "SZ301059", "SZ301061", "SZ301063", "SZ301065", "SZ301067", "SZ301068", "SZ301069", "SZ301071", "SZ301072", "SZ301073", "SZ301075", "SZ301076", "SZ301077", "SZ301080", "SZ301081", "SZ301083", "SZ301087", "SZ301088", "SZ301089", "SZ301090", "SZ301091", "SZ301093", "SZ301096", "SZ301099", "SZ301101", "SZ301107", "SZ301108", "SZ301109", "SZ301119", "SZ301120", "SZ301127", "SZ301129", "SZ301130", "SZ301132", "SZ301135", "SZ301136", "SZ301137", "SZ301138", "SZ301148", "SZ301150", "SZ301151", "SZ301155", "SZ301157", "SZ301158", "SZ301161", "SZ301162", "SZ301167", "SZ301171", "SZ301172", "SZ301179", "SZ301181", "SZ301182", "SZ301187", "SZ301188", "SZ301189", "SZ301190", "SZ301191", "SZ301192", "SZ301195", "SZ301196", "SZ301198", "SZ301200", "SZ301201", "SZ301202", "SZ301203", "SZ301207", "SZ301208", "SZ301213", "SZ301215", "SZ301218", "SZ301219", "SZ301222", "SZ301227", "SZ301230", "SZ301231", "SZ301235", "SZ301237", "SZ301238", "SZ301239", "SZ301248", "SZ301252", "SZ301256", "SZ301257", "SZ301260", "SZ301261", "SZ301262", "SZ301263", "SZ301265", "SZ301267", "SZ301269", "SZ301270", "SZ301273", "SZ301276", "SZ301277", "SZ301280", "SZ301289", "SZ301291", "SZ301292", "SZ301296", "SZ301298", "SZ301299", "SZ301302", "SZ301303", "SZ301305", "SZ301306", "SZ301308", "SZ301311", "SZ301312", "SZ301313", "SZ301316", "SZ301319", "SZ301320", "SZ301323", "SZ301325", "SZ301326", "SZ301327", "SZ301330", "SZ301333", "SZ301338", "SZ301345", "SZ301348", "SZ301353", "SZ301355", "SZ301357", "SZ301358", "SZ301360", "SZ301362", "SZ301367", "SZ301368", "SZ301370", "SZ301371", "SZ301373", "SZ301380", "SZ301382", "SZ301386", "SZ301388", "SZ301391", "SZ301393", "SZ301397", "SZ301398", "SZ301408", "SZ301418", "SZ301421", "SZ301429", "SZ301439", "SZ301468", "SZ301487", "SZ301488", "SZ301503", "SZ301505", "SZ301510", "SZ301512", "SZ301515", "SZ301528"], "sz_count": 992, "sh_count": 608, "by_exchange": {"SZ": ["SZ000006", "SZ000009", "SZ000011", "SZ000012", "SZ000014", "SZ000016", "SZ000017", "SZ000019", "SZ000020", "SZ000021", "SZ000025", "SZ000027", "SZ000028", "SZ000029", "SZ000031", "SZ000032", "SZ000035", "SZ000036", "SZ000037", "SZ000045", "SZ000050", "SZ000055", "SZ000056", "SZ000058", "SZ000059", "SZ000061", "SZ000062", "SZ000063", "SZ000065", "SZ000066", "SZ000068", "SZ000069", "SZ000078", "SZ000089", "SZ000099", "SZ000151", "SZ000156", "SZ000159", "SZ000166", "SZ000338", "SZ000400", "SZ000401", "SZ000403", "SZ000408", "SZ000410", "SZ000415", "SZ000417", "SZ000420", "SZ000421", "SZ000422", "SZ000423", "SZ000428", "SZ000501", "SZ000503", "SZ000507", "SZ000509", "SZ000510", "SZ000513", "SZ000514", "SZ000516", "SZ000517", "SZ000519", "SZ000520", "SZ000528", "SZ000530", "SZ000531", "SZ000532", "SZ000536", "SZ000544", "SZ000548", "SZ000550", "SZ000551", "SZ000552", "SZ000554", "SZ000557", "SZ000560", "SZ000561", "SZ000565", "SZ000566", "SZ000567", "SZ000568", "SZ000571", "SZ000572", "SZ000576", "SZ000582", "SZ000586", "SZ000589", "SZ000590", "SZ000596", "SZ000597", "SZ000598", "SZ000603", "SZ000605", "SZ000733", "SZ000737", "SZ000739", "SZ000750", "SZ000917", "SZ000920", "SZ000921", "SZ000923", "SZ001267", "SZ001269", "SZ001283", "SZ001311", "SZ001872", "SZ001896", "SZ001914", "SZ001965", "SZ002344", "SZ002345", "SZ002346", "SZ002347", "SZ002350", "SZ002351", "SZ002353", "SZ002358", "SZ002360", "SZ002362", "SZ002364", "SZ002365", "SZ002366", "SZ002369", "SZ002371", "SZ002373", "SZ002376", "SZ002377", "SZ002381", "SZ002382", "SZ002383", "SZ002385", "SZ002392", "SZ002393", "SZ002395", "SZ002396", "SZ002397", "SZ002398", "SZ002399", "SZ002400", "SZ002402", "SZ002403", "SZ002404", "SZ002405", "SZ002407", "SZ002409", "SZ002412", "SZ002415", "SZ002416", "SZ002420", "SZ002421", "SZ002424", "SZ002427", "SZ002430", "SZ002431", "SZ002437", "SZ002438", "SZ002439", "SZ002445", "SZ002446", "SZ002448", "SZ002449", "SZ002451", "SZ002453", "SZ002454", "SZ002455", "SZ002456", "SZ002459", "SZ002461", "SZ002462", "SZ002463", "SZ002465", "SZ002468", "SZ002472", "SZ002474", "SZ002480", "SZ002481", "SZ002483", "SZ002484", "SZ002486", "SZ002487", "SZ002489", "SZ002493", "SZ002494", "SZ002497", "SZ002498", "SZ002501", "SZ002506", "SZ002507", "SZ002513", "SZ002516", "SZ002520", "SZ002522", "SZ002527", "SZ002534", "SZ002535", "SZ002536", "SZ002537", "SZ002538", "SZ002542", "SZ002544", "SZ002546", "SZ002547", "SZ002549", "SZ002550", "SZ002553", "SZ002557", "SZ002563", "SZ002567", "SZ002568", "SZ002570", "SZ002573", "SZ002574", "SZ002579", "SZ002580", "SZ002581", "SZ002583", "SZ002584", "SZ002585", "SZ002587", "SZ002588", "SZ002589", "SZ002593", "SZ002595", "SZ002596", "SZ002599", "SZ002605", "SZ002606", "SZ002607", "SZ002608", "SZ002609", "SZ002612", "SZ002613", "SZ002615", "SZ002622", "SZ002628", "SZ002633", "SZ002634", "SZ002638", "SZ002642", "SZ002645", "SZ002648", "SZ002649", "SZ002651", "SZ002652", "SZ002653", "SZ002659", "SZ002660", "SZ002663", "SZ002668", "SZ002670", "SZ002673", "SZ002674", "SZ002675", "SZ002677", "SZ002678", "SZ002685", "SZ002691", "SZ002695", "SZ002696", "SZ002698", "SZ002702", "SZ002703", "SZ002705", "SZ002714", "SZ002715", "SZ002716", "SZ002718", "SZ002721", "SZ002722", "SZ002724", "SZ002728", "SZ002730", "SZ002731", "SZ002732", "SZ002733", "SZ002734", "SZ002735", "SZ002736", "SZ002737", "SZ002745", "SZ002747", "SZ002748", "SZ002749", "SZ002752", "SZ002753", "SZ002756", "SZ002757", "SZ002758", "SZ002760", "SZ002761", "SZ002763", "SZ002765", "SZ002768", "SZ002769", "SZ002775", "SZ002778", "SZ002779", "SZ002782", "SZ002783", "SZ002787", "SZ002790", "SZ002791", "SZ002795", "SZ002799", "SZ002802", "SZ002803", "SZ002806", "SZ002807", "SZ002820", "SZ002821", "SZ002823", "SZ002824", "SZ002826", "SZ002828", "SZ002829", "SZ002830", "SZ002831", "SZ002832", "SZ002835", "SZ002836", "SZ002837", "SZ002840", "SZ002841", "SZ002843", "SZ002845", "SZ002846", "SZ002853", "SZ002855", "SZ002858", "SZ002859", "SZ002860", "SZ002861", "SZ002867", "SZ002873", "SZ002877", "SZ002880", "SZ002884", "SZ002886", "SZ002891", "SZ002892", "SZ002896", "SZ002897", "SZ002899", "SZ002900", "SZ002901", "SZ002906", "SZ002907", "SZ002908", "SZ002912", "SZ002913", "SZ002915", "SZ002916", "SZ002918", "SZ002919", "SZ002923", "SZ002926", "SZ002927", "SZ002928", "SZ002929", "SZ002931", "SZ002932", "SZ002933", "SZ002940", "SZ002941", "SZ002942", "SZ002945", "SZ002946", "SZ002949", "SZ002953", "SZ002956", "SZ002961", "SZ002963", "SZ002966", "SZ002968", "SZ002969", "SZ002971", "SZ002973", "SZ002975", "SZ002976", "SZ002980", "SZ002981", "SZ002982", "SZ002984", "SZ002985", "SZ002988", "SZ002989", "SZ002991", "SZ002997", "SZ002998", "SZ003000", "SZ003001", "SZ003002", "SZ003006", "SZ003007", "SZ003009", "SZ003011", "SZ003012", "SZ003013", "SZ003015", "SZ003016", "SZ003017", "SZ003026", "SZ003027", "SZ003028", "SZ003029", "SZ003035", "SZ003036", "SZ003040", "SZ003041", "SZ003042", "SZ003043", "SZ003816", "SZ300001", "SZ300004", "SZ300005", "SZ300006", "SZ300007", "SZ300010", "SZ300012", "SZ300013", "SZ300015", "SZ300016", "SZ300018", "SZ300021", "SZ300022", "SZ300024", "SZ300025", "SZ300027", "SZ300033", "SZ300037", "SZ300041", "SZ300042", "SZ300043", "SZ300045", "SZ300046", "SZ300048", "SZ300051", "SZ300056", "SZ300058", "SZ300061", "SZ300065", "SZ300068", "SZ300072", "SZ300074", "SZ300075", "SZ300077", "SZ300079", "SZ300080", "SZ300082", "SZ300083", "SZ300084", "SZ300087", "SZ300094", "SZ300095", "SZ300100", "SZ300101", "SZ300106", "SZ300109", "SZ300110", "SZ300111", "SZ300119", "SZ300122", "SZ300124", "SZ300129", "SZ300130", "SZ300131", "SZ300133", "SZ300134", "SZ300138", "SZ300139", "SZ300141", "SZ300142", "SZ300143", "SZ300145", "SZ300146", "SZ300151", "SZ300154", "SZ300157", "SZ300158", "SZ300161", "SZ300170", "SZ300179", "SZ300180", "SZ300182", "SZ300183", "SZ300184", "SZ300187", "SZ300188", "SZ300191", "SZ300201", "SZ300206", "SZ300207", "SZ300209", "SZ300210", "SZ300212", "SZ300214", "SZ300215", "SZ300223", "SZ300224", "SZ300229", "SZ300230", "SZ300232", "SZ300235", "SZ300238", "SZ300241", "SZ300243", "SZ300247", "SZ300248", "SZ300250", "SZ300253", "SZ300256", "SZ300259", "SZ300260", "SZ300270", "SZ300271", "SZ300275", "SZ300277", "SZ300278", "SZ300279", "SZ300283", "SZ300285", "SZ300288", "SZ300289", "SZ300290", "SZ300291", "SZ300292", "SZ300293", "SZ300294", "SZ300296", "SZ300302", "SZ300305", "SZ300307", "SZ300314", "SZ300315", "SZ300316", "SZ300317", "SZ300318", "SZ300320", "SZ300321", "SZ300323", "SZ300324", "SZ300329", "SZ300334", "SZ300339", "SZ300345", "SZ300346", "SZ300347", "SZ300350", "SZ300351", "SZ300352", "SZ300354", "SZ300355", "SZ300357", "SZ300358", "SZ300360", "SZ300363", "SZ300364", "SZ300366", "SZ300368", "SZ300371", "SZ300373", "SZ300381", "SZ300382", "SZ300393", "SZ300395", "SZ300400", "SZ300403", "SZ300404", "SZ300407", "SZ300410", "SZ300412", "SZ300414", "SZ300417", "SZ300418", "SZ300421", "SZ300422", "SZ300423", "SZ300425", "SZ300426", "SZ300430", "SZ300433", "SZ300437", "SZ300442", "SZ300444", "SZ300448", "SZ300449", "SZ300451", "SZ300454", "SZ300498", "SZ300500", "SZ300501", "SZ300502", "SZ300540", "SZ300542", "SZ300543", "SZ300546", "SZ300549", "SZ300550", "SZ300551", "SZ300556", "SZ300557", "SZ300558", "SZ300560", "SZ300566", "SZ300568", "SZ300573", "SZ300575", "SZ300578", "SZ300579", "SZ300580", "SZ300581", "SZ300582", "SZ300584", "SZ300588", "SZ300589", "SZ300592", "SZ300593", "SZ300594", "SZ300595", "SZ300596", "SZ300597", "SZ300598", "SZ300599", "SZ300600", "SZ300607", "SZ300609", "SZ300610", "SZ300614", "SZ300615", "SZ300622", "SZ300623", "SZ300626", "SZ300627", "SZ300629", "SZ300636", "SZ300637", "SZ300639", "SZ300641", "SZ300644", "SZ300645", "SZ300648", "SZ300649", "SZ300653", "SZ300655", "SZ300656", "SZ300659", "SZ300660", "SZ300661", "SZ300665", "SZ300666", "SZ300668", "SZ300669", "SZ300670", "SZ300672", "SZ300675", "SZ300676", "SZ300679", "SZ300681", "SZ300682", "SZ300683", "SZ300686", "SZ300688", "SZ300689", "SZ300690", "SZ300694", "SZ300696", "SZ300697", "SZ300701", "SZ300703", "SZ300712", "SZ300713", "SZ300715", "SZ300717", "SZ300720", "SZ300722", "SZ300724", "SZ300725", "SZ300727", "SZ300730", "SZ300731", "SZ300732", "SZ300733", "SZ300736", "SZ300739", "SZ300740", "SZ300741", "SZ300745", "SZ300746", "SZ300753", "SZ300755", "SZ300765", "SZ300766", "SZ300770", "SZ300771", "SZ300773", "SZ300777", "SZ300778", "SZ300781", "SZ300782", "SZ300786", "SZ300787", "SZ300788", "SZ300789", "SZ300792", "SZ300796", "SZ300800", "SZ300801", "SZ300803", "SZ300805", "SZ300806", "SZ300809", "SZ300812", "SZ300813", "SZ300814", "SZ300815", "SZ300817", "SZ300820", "SZ300822", "SZ300823", "SZ300824", "SZ300826", "SZ300829", "SZ300831", "SZ300832", "SZ300833", "SZ300834", "SZ300837", "SZ300838", "SZ300839", "SZ300845", "SZ300850", "SZ300852", "SZ300853", "SZ300854", "SZ300855", "SZ300857", "SZ300859", "SZ300860", "SZ300862", "SZ300863", "SZ300864", "SZ300865", "SZ300867", "SZ300868", "SZ300870", "SZ300871", "SZ300873", "SZ300875", "SZ300876", "SZ300879", "SZ300880", "SZ300884", "SZ300885", "SZ300886", "SZ300887", "SZ300888", "SZ300889", "SZ300892", "SZ300893", "SZ300894", "SZ300896", "SZ300898", "SZ300902", "SZ300904", "SZ300906", "SZ300909", "SZ300910", "SZ300912", "SZ300915", "SZ300916", "SZ300917", "SZ300918", "SZ300919", "SZ300920", "SZ300921", "SZ300925", "SZ300926", "SZ300927", "SZ300928", "SZ300929", "SZ300935", "SZ300937", "SZ300940", "SZ300941", "SZ300942", "SZ300943", "SZ300945", "SZ300948", "SZ300951", "SZ300955", "SZ300957", "SZ300961", "SZ300964", "SZ300967", "SZ300968", "SZ300969", "SZ300970", "SZ300971", "SZ300975", "SZ300976", "SZ300977", "SZ300979", "SZ300983", "SZ300988", "SZ300989", "SZ300990", "SZ300991", "SZ300992", "SZ300993", "SZ300996", "SZ300997", "SZ301001", "SZ301003", "SZ301004", "SZ301009", "SZ301010", "SZ301011", "SZ301012", "SZ301013", "SZ301015", "SZ301017", "SZ301018", "SZ301021", "SZ301022", "SZ301023", "SZ301025", "SZ301026", "SZ301027", "SZ301028", "SZ301032", "SZ301035", "SZ301036", "SZ301037", "SZ301038", "SZ301052", "SZ301056", "SZ301057", "SZ301058", "SZ301059", "SZ301061", "SZ301063", "SZ301065", "SZ301067", "SZ301068", "SZ301069", "SZ301071", "SZ301072", "SZ301073", "SZ301075", "SZ301076", "SZ301077", "SZ301080", "SZ301081", "SZ301083", "SZ301087", "SZ301088", "SZ301089", "SZ301090", "SZ301091", "SZ301093", "SZ301096", "SZ301099", "SZ301101", "SZ301107", "SZ301108", "SZ301109", "SZ301119", "SZ301120", "SZ301127", "SZ301129", "SZ301130", "SZ301132", "SZ301135", "SZ301136", "SZ301137", "SZ301138", "SZ301148", "SZ301150", "SZ301151", "SZ301155", "SZ301157", "SZ301158", "SZ301161", "SZ301162", "SZ301167", "SZ301171", "SZ301172", "SZ301179", "SZ301181", "SZ301182", "SZ301187", "SZ301188", "SZ301189", "SZ301190", "SZ301191", "SZ301192", "SZ301195", "SZ301196", "SZ301198", "SZ301200", "SZ301201", "SZ301202", "SZ301203", "SZ301207", "SZ301208", "SZ301213", "SZ301215", "SZ301218", "SZ301219", "SZ301222", "SZ301227", "SZ301230", "SZ301231", "SZ301235", "SZ301237", "SZ301238", "SZ301239", "SZ301248", "SZ301252", "SZ301256", "SZ301257", "SZ301260", "SZ301261", "SZ301262", "SZ301263", "SZ301265", "SZ301267", "SZ301269", "SZ301270", "SZ301273", "SZ301276", "SZ301277", "SZ301280", "SZ301289", "SZ301291", "SZ301292", "SZ301296", "SZ301298", "SZ301299", "SZ301302", "SZ301303", "SZ301305", "SZ301306", "SZ301308", "SZ301311", "SZ301312", "SZ301313", "SZ301316", "SZ301319", "SZ301320", "SZ301323", "SZ301325", "SZ301326", "SZ301327", "SZ301330", "SZ301333", "SZ301338", "SZ301345", "SZ301348", "SZ301353", "SZ301355", "SZ301357", "SZ301358", "SZ301360", "SZ301362", "SZ301367", "SZ301368", "SZ301370", "SZ301371", "SZ301373", "SZ301380", "SZ301382", "SZ301386", "SZ301388", "SZ301391", "SZ301393", "SZ301397", "SZ301398", "SZ301408", "SZ301418", "SZ301421", "SZ301429", "SZ301439", "SZ301468", "SZ301487", "SZ301488", "SZ301503", "SZ301505", "SZ301510", "SZ301512", "SZ301515", "SZ301528"], "SH": ["SH600252", "SH600354", "SH600359", "SH600361", "SH600362", "SH600386", "SH600389", "SH600396", "SH600398", "SH600488", "SH600587", "SH600588", "SH600589", "SH600590", "SH600640", "SH600642", "SH600643", "SH600648", "SH600649", "SH600653", "SH600654", "SH600655", "SH600658", "SH600661", "SH600662", "SH603053", "SH603055", "SH603058", "SH603060", "SH603067", "SH603068", "SH603069", "SH603071", "SH603077", "SH603078", "SH603079", "SH603080", "SH603081", "SH603083", "SH603087", "SH603089", "SH603093", "SH603096", "SH603097", "SH603098", "SH603101", "SH603102", "SH603103", "SH603108", "SH603112", "SH603115", "SH603118", "SH603119", "SH603121", "SH603122", "SH603126", "SH603127", "SH603128", "SH603129", "SH603132", "SH603137", "SH603138", "SH603139", "SH603150", "SH603155", "SH603156", "SH603158", "SH603160", "SH603162", "SH603163", "SH603165", "SH603167", "SH603168", "SH603171", "SH603176", "SH603178", "SH603179", "SH603181", "SH603182", "SH603183", "SH603186", "SH603187", "SH603191", "SH603192", "SH603196", "SH603197", "SH603206", "SH603208", "SH603209", "SH603211", "SH603214", "SH603215", "SH603216", "SH603218", "SH603221", "SH603222", "SH603225", "SH603226", "SH603228", "SH603230", "SH603232", "SH603233", "SH603236", "SH603238", "SH603239", "SH603258", "SH603259", "SH603260", "SH603269", "SH603277", "SH603278", "SH603279", "SH603280", "SH603281", "SH603286", "SH603288", "SH603289", "SH603290", "SH603296", "SH603297", "SH603298", "SH603300", "SH603301", "SH603305", "SH603306", "SH603307", "SH603313", "SH603316", "SH603318", "SH603321", "SH603326", "SH603331", "SH603337", "SH603338", "SH603339", "SH603348", "SH603353", "SH603355", "SH603356", "SH603357", "SH603358", "SH603360", "SH603365", "SH603366", "SH603368", "SH603380", "SH603385", "SH603387", "SH603393", "SH603399", "SH603408", "SH603439", "SH603444", "SH603458", "SH603501", "SH603506", "SH603507", "SH603511", "SH603515", "SH603516", "SH603520", "SH603528", "SH603529", "SH603533", "SH603535", "SH603556", "SH603566", "SH603567", "SH603577", "SH603579", "SH603583", "SH603587", "SH603589", "SH603600", "SH603601", "SH603605", "SH603611", "SH603613", "SH603615", "SH603617", "SH603618", "SH603619", "SH603628", "SH603629", "SH603630", "SH603636", "SH603639", "SH603648", "SH603655", "SH603656", "SH603657", "SH603659", "SH603660", "SH603662", "SH603669", "SH603676", "SH603678", "SH603679", "SH603681", "SH603683", "SH603685", "SH603687", "SH603689", "SH603693", "SH603697", "SH603716", "SH603718", "SH603726", "SH603727", "SH603738", "SH603739", "SH603755", "SH603757", "SH603759", "SH603773", "SH603776", "SH603777", "SH603778", "SH603787", "SH603799", "SH603800", "SH603803", "SH603808", "SH603809", "SH603811", "SH603817", "SH603818", "SH603822", "SH603823", "SH603825", "SH603826", "SH603829", "SH603833", "SH603836", "SH603855", "SH603858", "SH603860", "SH603861", "SH603863", "SH603866", "SH603867", "SH603871", "SH603878", "SH603879", "SH603880", "SH603881", "SH603885", "SH603887", "SH603888", "SH603889", "SH603890", "SH603893", "SH603899", "SH603900", "SH603901", "SH603903", "SH603908", "SH603915", "SH603916", "SH603917", "SH603919", "SH603920", "SH603928", "SH603931", "SH603936", "SH603937", "SH603955", "SH603958", "SH603966", "SH603967", "SH603969", "SH603970", "SH603976", "SH603977", "SH603978", "SH603980", "SH603982", "SH603983", "SH603992", "SH603993", "SH603995", "SH603998", "SH603999", "SH605005", "SH605009", "SH605011", "SH605016", "SH605020", "SH605056", "SH605058", "SH605068", "SH605069", "SH605077", "SH605080", "SH605088", "SH605089", "SH605098", "SH605100", "SH605108", "SH605111", "SH605118", "SH605122", "SH605123", "SH605136", "SH605151", "SH605155", "SH605158", "SH605162", "SH605167", "SH605169", "SH605177", "SH605178", "SH605179", "SH605180", "SH605188", "SH605196", "SH605208", "SH605258", "SH605268", "SH605277", "SH605286", "SH605288", "SH605303", "SH605318", "SH605319", "SH605333", "SH605337", "SH605339", "SH605368", "SH605369", "SH605376", "SH605377", "SH605388", "SH605398", "SH605399", "SH605499", "SH605500", "SH605507", "SH605567", "SH605588", "SH605589", "SH605599", "SH688001", "SH688004", "SH688006", "SH688008", "SH688009", "SH688011", "SH688012", "SH688020", "SH688022", "SH688025", "SH688026", "SH688027", "SH688028", "SH688029", "SH688031", "SH688041", "SH688046", "SH688049", "SH688050", "SH688056", "SH688058", "SH688059", "SH688061", "SH688063", "SH688065", "SH688067", "SH688068", "SH688071", "SH688072", "SH688073", "SH688076", "SH688077", "SH688080", "SH688083", "SH688084", "SH688088", "SH688089", "SH688090", "SH688092", "SH688097", "SH688098", "SH688099", "SH688102", "SH688103", "SH688105", "SH688109", "SH688110", "SH688114", "SH688117", "SH688118", "SH688121", "SH688122", "SH688126", "SH688127", "SH688128", "SH688130", "SH688131", "SH688133", "SH688135", "SH688138", "SH688139", "SH688141", "SH688150", "SH688151", "SH688153", "SH688157", "SH688159", "SH688160", "SH688165", "SH688166", "SH688167", "SH688172", "SH688177", "SH688180", "SH688182", "SH688183", "SH688187", "SH688188", "SH688189", "SH688191", "SH688192", "SH688195", "SH688196", "SH688200", "SH688203", "SH688206", "SH688207", "SH688209", "SH688212", "SH688215", "SH688216", "SH688218", "SH688221", "SH688222", "SH688223", "SH688227", "SH688229", "SH688232", "SH688235", "SH688236", "SH688238", "SH688244", "SH688246", "SH688248", "SH688249", "SH688253", "SH688255", "SH688257", "SH688258", "SH688259", "SH688260", "SH688262", "SH688265", "SH688266", "SH688268", "SH688269", "SH688276", "SH688278", "SH688281", "SH688282", "SH688283", "SH688286", "SH688289", "SH688290", "SH688292", "SH688295", "SH688296", "SH688297", "SH688302", "SH688303", "SH688306", "SH688308", "SH688311", "SH688312", "SH688314", "SH688315", "SH688318", "SH688319", "SH688323", "SH688328", "SH688331", "SH688334", "SH688335", "SH688337", "SH688345", "SH688347", "SH688348", "SH688353", "SH688355", "SH688362", "SH688363", "SH688366", "SH688368", "SH688370", "SH688371", "SH688373", "SH688375", "SH688377", "SH688379", "SH688380", "SH688383", "SH688386", "SH688388", "SH688389", "SH688390", "SH688395", "SH688396", "SH688400", "SH688403", "SH688408", "SH688428", "SH688429", "SH688432", "SH688433", "SH688435", "SH688439", "SH688443", "SH688456", "SH688459", "SH688475", "SH688478", "SH688479", "SH688484", "SH688496", "SH688498", "SH688499", "SH688500", "SH688502", "SH688505", "SH688506", "SH688509", "SH688512", "SH688515", "SH688519", "SH688526", "SH688529", "SH688531", "SH688535", "SH688538", "SH688543", "SH688550", "SH688553", "SH688557", "SH688558", "SH688559", "SH688560", "SH688565", "SH688566", "SH688571", "SH688576", "SH688577", "SH688586", "SH688588", "SH688589", "SH688590", "SH688591", "SH688592", "SH688593", "SH688596", "SH688597", "SH688600", "SH688601", "SH688610", "SH688611", "SH688616", "SH688617", "SH688623", "SH688625", "SH688629", "SH688631", "SH688633", "SH688636", "SH688638", "SH688639", "SH688651", "SH688655", "SH688656", "SH688658", "SH688660", "SH688661", "SH688663", "SH688665", "SH688667", "SH688677", "SH688680", "SH688683", "SH688686", "SH688687", "SH688696", "SH688699", "SH688701", "SH688707", "SH688718", "SH688722", "SH688728", "SH688733", "SH688739", "SH688766", "SH688767", "SH688772", "SH688776", "SH688777", "SH688786", "SH688787", "SH688799", "SH688800"]}}, "data_quality": {"csv_file": "股票代码列表.csv", "db_file": "merged_stock_analysis.db", "csv_sample_codes": ["SZ002957", "SZ002970", "SH688400", "SZ300543", "SZ002840", "SZ002367", "SZ002559", "SZ300759", "SH688267", "SZ301038"], "db_sample_codes": ["SZ002957", "SZ002970", "SZ002367", "SZ002559", "SZ300759", "SH688267", "SZ002265", "SH688052", "SH600336", "SZ300073"]}}