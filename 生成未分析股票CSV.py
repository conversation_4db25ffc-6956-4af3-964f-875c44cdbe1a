#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成未分析股票清单CSV文件
基于股票分析完整性检查结果，创建包含未分析股票信息的CSV文件
"""

import json
import csv
import sqlite3
from datetime import datetime

class UnanalyzedStockCSVGenerator:
    def __init__(self, json_report_file='股票分析完整性报告_20250722_094306.json', 
                 db_file='merged_stock_analysis.db',
                 output_file='未分析股票清单.csv'):
        self.json_report_file = json_report_file
        self.db_file = db_file
        self.output_file = output_file
        self.missing_stocks = []
        self.stock_names = {}
        
    def load_missing_stocks(self):
        """从JSON报告中加载未分析股票列表"""
        try:
            with open(self.json_report_file, 'r', encoding='utf-8') as f:
                report = json.load(f)
            
            self.missing_stocks = report['missing_stocks']['list']
            print(f"从报告中加载了 {len(self.missing_stocks)} 只未分析股票")
            
        except Exception as e:
            print(f"加载JSON报告失败: {e}")
            raise
    
    def get_stock_names_from_db(self):
        """从数据库中获取已知的股票名称"""
        try:
            conn = sqlite3.connect(self.db_file)
            cursor = conn.cursor()
            
            # 查询所有已分析股票的名称
            query = "SELECT DISTINCT stock_code, stock_name FROM stock_analysis_results"
            cursor.execute(query)
            
            for row in cursor:
                stock_code, stock_name = row
                self.stock_names[stock_code] = stock_name
            
            conn.close()
            print(f"从数据库中获取了 {len(self.stock_names)} 个股票名称")
            
        except Exception as e:
            print(f"从数据库获取股票名称失败: {e}")
    
    def get_stock_name(self, stock_code):
        """获取股票名称，如果没有则返回"待查询" """
        return self.stock_names.get(stock_code, "待查询")
    
    def sort_stock_codes(self):
        """按要求排序股票代码：先SH后SZ，代码内按数字升序"""
        def sort_key(code):
            if code.startswith('SH'):
                exchange = 'A'  # SH排在前面
                number = code[2:]
            elif code.startswith('SZ'):
                exchange = 'B'  # SZ排在后面
                number = code[2:]
            else:
                exchange = 'Z'  # 其他排在最后
                number = code
            
            return (exchange, int(number) if number.isdigit() else float('inf'))
        
        self.missing_stocks.sort(key=sort_key)
        print(f"股票代码排序完成，共 {len(self.missing_stocks)} 只")
    
    def generate_csv(self):
        """生成CSV文件"""
        try:
            with open(self.output_file, 'w', newline='', encoding='utf-8-sig') as csvfile:
                writer = csv.writer(csvfile)
                
                # 写入表头
                writer.writerow(['序号', '股票名称', '股票代码'])
                
                # 写入数据
                for i, stock_code in enumerate(self.missing_stocks, 1):
                    stock_name = self.get_stock_name(stock_code)
                    writer.writerow([i, stock_name, stock_code])
                
            print(f"CSV文件生成成功: {self.output_file}")
            print(f"包含 {len(self.missing_stocks)} 只未分析股票")
            
        except Exception as e:
            print(f"生成CSV文件失败: {e}")
            raise
    
    def print_summary(self):
        """打印生成摘要"""
        print("\n" + "="*50)
        print("📊 未分析股票清单CSV生成摘要")
        print("="*50)
        
        # 统计各交易所数量
        sh_count = sum(1 for code in self.missing_stocks if code.startswith('SH'))
        sz_count = sum(1 for code in self.missing_stocks if code.startswith('SZ'))
        
        print(f"📈 总体统计:")
        print(f"  • 未分析股票总数: {len(self.missing_stocks):,} 只")
        print(f"  • 上交所(SH): {sh_count:,} 只 ({sh_count/len(self.missing_stocks)*100:.1f}%)")
        print(f"  • 深交所(SZ): {sz_count:,} 只 ({sz_count/len(self.missing_stocks)*100:.1f}%)")
        
        # 统计有名称的股票数量
        named_count = sum(1 for code in self.missing_stocks if self.get_stock_name(code) != "待查询")
        print(f"  • 已获取名称: {named_count:,} 只 ({named_count/len(self.missing_stocks)*100:.1f}%)")
        print(f"  • 待查询名称: {len(self.missing_stocks)-named_count:,} 只")
        
        print(f"\n📁 输出文件:")
        print(f"  • 文件名: {self.output_file}")
        print(f"  • 编码格式: UTF-8 with BOM")
        print(f"  • 列结构: 序号, 股票名称, 股票代码")
        
        # 显示前10个和后10个股票作为示例
        print(f"\n📋 数据示例:")
        print(f"  前10只股票:")
        for i in range(min(10, len(self.missing_stocks))):
            code = self.missing_stocks[i]
            name = self.get_stock_name(code)
            print(f"    {i+1:3d}. {name:<12} {code}")
        
        if len(self.missing_stocks) > 10:
            print(f"  ...")
            print(f"  后5只股票:")
            for i in range(max(0, len(self.missing_stocks)-5), len(self.missing_stocks)):
                code = self.missing_stocks[i]
                name = self.get_stock_name(code)
                print(f"    {i+1:3d}. {name:<12} {code}")
        
        print(f"\n✅ CSV文件生成完成！")
    
    def run(self):
        """执行完整的生成流程"""
        print("🚀 开始生成未分析股票清单CSV文件...")
        
        # 1. 加载未分析股票列表
        self.load_missing_stocks()
        
        # 2. 从数据库获取股票名称
        self.get_stock_names_from_db()
        
        # 3. 排序股票代码
        self.sort_stock_codes()
        
        # 4. 生成CSV文件
        self.generate_csv()
        
        # 5. 打印摘要
        self.print_summary()

def main():
    """主函数"""
    try:
        generator = UnanalyzedStockCSVGenerator()
        generator.run()
        
    except Exception as e:
        print(f"❌ 生成过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
