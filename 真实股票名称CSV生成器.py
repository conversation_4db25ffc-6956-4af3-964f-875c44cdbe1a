#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
真实股票名称CSV生成器
从多个数据源获取真实的股票名称，生成高质量的未分析股票清单
"""

import json
import csv
import sqlite3
import re
import time
from datetime import datetime

class RealStockNameCSVGenerator:
    def __init__(self, json_report_file='股票分析完整性报告_20250722_094306.json', 
                 csv_source='股票代码列表.csv',
                 db_file='merged_stock_analysis.db',
                 output_file='未分析股票清单.csv'):
        self.json_report_file = json_report_file
        self.csv_source = csv_source
        self.db_file = db_file
        self.output_file = output_file
        self.missing_stocks = []
        self.stock_names = {}
        
    def load_missing_stocks(self):
        """从JSON报告中加载未分析股票列表"""
        try:
            with open(self.json_report_file, 'r', encoding='utf-8') as f:
                report = json.load(f)
            
            self.missing_stocks = report['missing_stocks']['list']
            print(f"从报告中加载了 {len(self.missing_stocks)} 只未分析股票")
            
        except Exception as e:
            print(f"加载JSON报告失败: {e}")
            raise
    
    def standardize_stock_code(self, code):
        """标准化股票代码格式"""
        if not code:
            return None
            
        code = str(code).strip().upper()
        
        # 移除可能的空格和特殊字符
        code = re.sub(r'[^\w.]', '', code)
        
        if '.' in code:
            # 格式如 000001.SZ 或 SZ000001
            parts = code.split('.')
            if len(parts) == 2:
                if parts[1] in ['SZ', 'SH']:
                    # 000001.SZ 格式
                    return f"{parts[1]}{parts[0].zfill(6)}"
                elif parts[0] in ['SZ', 'SH']:
                    # SZ.000001 格式（不太常见）
                    return f"{parts[0]}{parts[1].zfill(6)}"
        elif len(code) == 6 and code.isdigit():
            # 纯数字代码，需要判断交易所
            if code.startswith(('000', '002', '300')):
                return f"SZ{code}"
            elif code.startswith(('600', '601', '603', '688')):
                return f"SH{code}"
        elif len(code) == 8 and code.startswith(('SZ', 'SH')):
            # 已经是标准格式
            return code
        elif len(code) > 8:
            # 可能包含其他信息，尝试提取
            match = re.search(r'(SH|SZ)(\d{6})', code)
            if match:
                return f"{match.group(1)}{match.group(2)}"
        
        return None
    
    def clean_stock_name(self, name):
        """清理股票名称"""
        if not name:
            return None
            
        name = str(name).strip()
        
        # 移除常见的前缀
        prefixes_to_remove = ['XD', 'XR', 'DR', 'ST', '*ST', 'N', 'C']
        for prefix in prefixes_to_remove:
            if name.startswith(prefix):
                name = name[len(prefix):].strip()
        
        # 移除可能的编码错误字符
        name = re.sub(r'[^\u4e00-\u9fff\w\s]', '', name)
        
        # 移除多余空格
        name = re.sub(r'\s+', '', name)
        
        return name if name and len(name) > 0 else None
    
    def get_stock_names_from_csv(self):
        """从原始CSV文件中获取股票名称"""
        print("正在从原始CSV文件获取股票名称...")
        
        # 尝试多种编码格式
        encodings = ['utf-8', 'gbk', 'gb2312', 'utf-8-sig', 'cp936', 'big5']
        
        for encoding in encodings:
            try:
                print(f"尝试使用 {encoding} 编码...")
                with open(self.csv_source, 'r', encoding=encoding, newline='') as f:
                    # 读取前几行来判断格式
                    sample_lines = []
                    for i, line in enumerate(f):
                        sample_lines.append(line.strip())
                        if i >= 5:
                            break
                    
                    print(f"样本行: {sample_lines[:3]}")
                
                # 重新打开文件读取全部内容
                with open(self.csv_source, 'r', encoding=encoding, newline='') as f:
                    reader = csv.reader(f)
                    rows = list(reader)
                
                if not rows:
                    continue
                    
                headers = rows[0]
                data_rows = rows[1:]
                
                print(f"成功使用 {encoding} 编码读取CSV文件")
                print(f"列名: {headers}")
                print(f"数据行数: {len(data_rows)}")
                
                # 查找股票名称列和股票代码列
                name_col_idx = None
                code_col_idx = None
                
                for i, col in enumerate(headers):
                    col_clean = re.sub(r'[^\u4e00-\u9fff\w]', '', str(col).lower())
                    if '名称' in col or 'name' in col_clean or '简称' in col:
                        name_col_idx = i
                        print(f"找到名称列: {col} (索引: {i})")
                    elif 'code' in col_clean or '代码' in col or '股票代码' in col:
                        code_col_idx = i
                        print(f"找到代码列: {col} (索引: {i})")
                
                if name_col_idx is not None and code_col_idx is not None:
                    success_count = 0
                    for row_idx, row in enumerate(data_rows):
                        if len(row) > max(name_col_idx, code_col_idx):
                            raw_code = row[code_col_idx] if code_col_idx < len(row) else ""
                            raw_name = row[name_col_idx] if name_col_idx < len(row) else ""
                            
                            # 标准化股票代码和名称
                            standard_code = self.standardize_stock_code(raw_code)
                            clean_name = self.clean_stock_name(raw_name)
                            
                            if standard_code and clean_name:
                                self.stock_names[standard_code] = clean_name
                                success_count += 1
                    
                    print(f"从CSV中成功获取了 {success_count} 个股票名称")
                    return True
                else:
                    print(f"使用 {encoding} 编码时未找到合适的列")
                    
            except UnicodeDecodeError as e:
                print(f"编码 {encoding} 解析失败: {e}")
                continue
            except Exception as e:
                print(f"使用 {encoding} 编码时出错: {e}")
                continue
        
        print("所有编码格式都失败了")
        return False
    
    def get_stock_names_from_db(self):
        """从数据库中获取已知的股票名称"""
        try:
            print("正在从数据库获取股票名称...")
            conn = sqlite3.connect(self.db_file)
            cursor = conn.cursor()
            
            # 查询所有已分析股票的名称
            query = "SELECT DISTINCT stock_code, stock_name FROM stock_analysis_results WHERE stock_name IS NOT NULL AND stock_name != ''"
            cursor.execute(query)
            
            db_count = 0
            for row in cursor:
                stock_code, stock_name = row
                clean_name = self.clean_stock_name(stock_name)
                if clean_name and stock_code not in self.stock_names:
                    self.stock_names[stock_code] = clean_name
                    db_count += 1
            
            conn.close()
            print(f"从数据库中获取了 {db_count} 个股票名称")
            
        except Exception as e:
            print(f"从数据库获取股票名称失败: {e}")
    
    def get_stock_name_by_pattern(self, stock_code):
        """根据股票代码模式推测可能的名称"""
        # 这里可以根据一些已知的模式来推测
        # 但主要还是依赖真实数据源
        return None
    
    def sort_stock_codes(self):
        """按要求排序股票代码：先SH后SZ，代码内按数字升序"""
        def sort_key(code):
            if code.startswith('SH'):
                exchange = 'A'  # SH排在前面
                number = code[2:]
            elif code.startswith('SZ'):
                exchange = 'B'  # SZ排在后面
                number = code[2:]
            else:
                exchange = 'Z'  # 其他排在最后
                number = code
            
            return (exchange, int(number) if number.isdigit() else float('inf'))
        
        self.missing_stocks.sort(key=sort_key)
        print(f"股票代码排序完成，共 {len(self.missing_stocks)} 只")
    
    def get_stock_name(self, stock_code):
        """获取股票名称"""
        return self.stock_names.get(stock_code, "待查询")
    
    def generate_csv(self):
        """生成CSV文件"""
        try:
            with open(self.output_file, 'w', newline='', encoding='utf-8-sig') as csvfile:
                writer = csv.writer(csvfile)
                
                # 写入表头
                writer.writerow(['序号', '股票名称', '股票代码'])
                
                # 写入数据
                for i, stock_code in enumerate(self.missing_stocks, 1):
                    stock_name = self.get_stock_name(stock_code)
                    writer.writerow([i, stock_name, stock_code])
                
            print(f"CSV文件生成成功: {self.output_file}")
            
        except Exception as e:
            print(f"生成CSV文件失败: {e}")
            raise
    
    def print_summary(self):
        """打印生成摘要"""
        print("\n" + "="*60)
        print("📊 真实股票名称CSV生成摘要")
        print("="*60)
        
        # 统计各交易所数量
        sh_count = sum(1 for code in self.missing_stocks if code.startswith('SH'))
        sz_count = sum(1 for code in self.missing_stocks if code.startswith('SZ'))
        
        # 统计名称获取情况
        real_names = sum(1 for code in self.missing_stocks if self.get_stock_name(code) != "待查询")
        unknown_names = len(self.missing_stocks) - real_names
        
        print(f"📈 总体统计:")
        print(f"  • 未分析股票总数: {len(self.missing_stocks):,} 只")
        print(f"  • 上交所(SH): {sh_count:,} 只 ({sh_count/len(self.missing_stocks)*100:.1f}%)")
        print(f"  • 深交所(SZ): {sz_count:,} 只 ({sz_count/len(self.missing_stocks)*100:.1f}%)")
        
        print(f"\n📝 名称获取情况:")
        print(f"  • 获取到真实名称: {real_names:,} 只 ({real_names/len(self.missing_stocks)*100:.1f}%)")
        print(f"  • 待查询名称: {unknown_names:,} 只 ({unknown_names/len(self.missing_stocks)*100:.1f}%)")
        
        print(f"\n📁 输出文件:")
        print(f"  • 文件名: {self.output_file}")
        print(f"  • 编码格式: UTF-8 with BOM")
        print(f"  • 列结构: 序号, 股票名称, 股票代码")
        print(f"  • 排序方式: SH在前，SZ在后，代码内按数字升序")
        
        # 显示真实名称的示例
        print(f"\n📋 真实名称示例:")
        real_name_examples = []
        for code in self.missing_stocks:
            name = self.get_stock_name(code)
            if name != "待查询":
                real_name_examples.append((name, code))
                if len(real_name_examples) >= 15:
                    break
        
        for i, (name, code) in enumerate(real_name_examples, 1):
            print(f"  {i:2d}. {name:<15} {code}")
        
        if unknown_names > 0:
            print(f"\n⚠️  注意: 还有 {unknown_names} 只股票的名称需要进一步查询")
        
        print(f"\n✅ 真实股票名称CSV文件生成完成！")
    
    def run(self):
        """执行完整的生成流程"""
        print("🚀 开始生成包含真实股票名称的CSV文件...")
        
        # 1. 加载未分析股票列表
        self.load_missing_stocks()
        
        # 2. 从原始CSV获取股票名称
        csv_success = self.get_stock_names_from_csv()
        
        # 3. 从数据库补充股票名称
        self.get_stock_names_from_db()
        
        # 4. 排序股票代码
        self.sort_stock_codes()
        
        # 5. 生成CSV文件
        self.generate_csv()
        
        # 6. 打印摘要
        self.print_summary()

def main():
    """主函数"""
    try:
        generator = RealStockNameCSVGenerator()
        generator.run()
        
    except Exception as e:
        print(f"❌ 生成过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
