#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库分析和合并工具
分析多个SQLite数据库文件的结构和内容，提供合并建议
"""

import sqlite3
import os
from pathlib import Path
import json
from datetime import datetime

class DatabaseAnalyzer:
    def __init__(self, directory_path):
        self.directory_path = Path(directory_path)
        self.db_files = []
        self.analysis_results = {}
        
    def find_database_files(self):
        """扫描目录中的所有数据库文件"""
        extensions = ['.db', '.sqlite', '.sqlite3']
        for ext in extensions:
            self.db_files.extend(self.directory_path.glob(f'**/*{ext}'))
        
        print(f"发现 {len(self.db_files)} 个数据库文件:")
        for db_file in self.db_files:
            size_mb = db_file.stat().st_size / (1024 * 1024)
            print(f"  - {db_file.name}: {size_mb:.2f} MB")
        
        return self.db_files
    
    def analyze_database_structure(self, db_path):
        """分析单个数据库的结构"""
        try:
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # 获取所有表名
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'")
            tables = [row[0] for row in cursor.fetchall()]
            
            db_info = {
                'file_path': str(db_path),
                'file_size_mb': db_path.stat().st_size / (1024 * 1024),
                'tables': {},
                'total_records': 0
            }
            
            for table in tables:
                # 获取表结构
                cursor.execute(f"PRAGMA table_info({table})")
                columns = cursor.fetchall()
                
                # 获取记录数
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                record_count = cursor.fetchone()[0]
                
                # 获取创建语句
                cursor.execute(f"SELECT sql FROM sqlite_master WHERE type='table' AND name='{table}'")
                create_sql = cursor.fetchone()[0]
                
                # 获取样本数据
                cursor.execute(f"SELECT * FROM {table} LIMIT 3")
                sample_data = cursor.fetchall()
                
                db_info['tables'][table] = {
                    'columns': columns,
                    'record_count': record_count,
                    'create_sql': create_sql,
                    'sample_data': sample_data
                }
                
                db_info['total_records'] += record_count
            
            conn.close()
            return db_info
            
        except Exception as e:
            print(f"分析数据库 {db_path} 时出错: {e}")
            return None
    
    def analyze_all_databases(self):
        """分析所有数据库文件"""
        print("\n开始分析数据库结构...")
        
        for db_file in self.db_files:
            print(f"\n分析: {db_file.name}")
            analysis = self.analyze_database_structure(db_file)
            if analysis:
                self.analysis_results[db_file.name] = analysis
                
                # 打印基本信息
                print(f"  文件大小: {analysis['file_size_mb']:.2f} MB")
                print(f"  表数量: {len(analysis['tables'])}")
                print(f"  总记录数: {analysis['total_records']}")
                
                for table_name, table_info in analysis['tables'].items():
                    print(f"    表 '{table_name}': {table_info['record_count']} 条记录")
    
    def compare_table_structures(self):
        """比较不同数据库中相同表名的结构"""
        print("\n\n=== 表结构比较分析 ===")
        
        # 收集所有表名
        all_tables = {}
        for db_name, db_info in self.analysis_results.items():
            for table_name in db_info['tables'].keys():
                if table_name not in all_tables:
                    all_tables[table_name] = []
                all_tables[table_name].append(db_name)
        
        # 分析每个表
        for table_name, db_list in all_tables.items():
            print(f"\n表名: {table_name}")
            print(f"出现在数据库: {', '.join(db_list)}")
            
            if len(db_list) > 1:
                # 比较结构
                structures = {}
                for db_name in db_list:
                    table_info = self.analysis_results[db_name]['tables'][table_name]
                    structures[db_name] = {
                        'columns': table_info['columns'],
                        'create_sql': table_info['create_sql'],
                        'record_count': table_info['record_count']
                    }
                
                # 检查结构是否相同
                first_db = db_list[0]
                first_structure = structures[first_db]['create_sql']
                
                structure_identical = True
                for db_name in db_list[1:]:
                    if structures[db_name]['create_sql'] != first_structure:
                        structure_identical = False
                        break
                
                if structure_identical:
                    print("  ✓ 结构相同，可以直接合并")
                    total_records = sum(structures[db]['record_count'] for db in db_list)
                    print(f"  总记录数: {total_records}")
                    for db_name in db_list:
                        print(f"    {db_name}: {structures[db_name]['record_count']} 条")
                else:
                    print("  ⚠ 结构不同，需要手动处理")
                    for db_name in db_list:
                        print(f"    {db_name}: {structures[db_name]['record_count']} 条记录")
                        print(f"      结构: {structures[db_name]['create_sql']}")
    
    def generate_merge_strategy(self):
        """生成合并策略"""
        print("\n\n=== 合并策略建议 ===")
        
        # 收集所有表名
        all_tables = {}
        for db_name, db_info in self.analysis_results.items():
            for table_name in db_info['tables'].keys():
                if table_name not in all_tables:
                    all_tables[table_name] = []
                all_tables[table_name].append(db_name)
        
        merge_strategy = {
            'direct_merge': [],  # 可以直接合并的表
            'manual_merge': [],  # 需要手动处理的表
            'unique_tables': []  # 只在一个数据库中存在的表
        }
        
        for table_name, db_list in all_tables.items():
            if len(db_list) == 1:
                merge_strategy['unique_tables'].append({
                    'table': table_name,
                    'database': db_list[0],
                    'records': self.analysis_results[db_list[0]]['tables'][table_name]['record_count']
                })
            else:
                # 检查结构是否相同
                first_db = db_list[0]
                first_structure = self.analysis_results[first_db]['tables'][table_name]['create_sql']
                
                structure_identical = True
                for db_name in db_list[1:]:
                    if self.analysis_results[db_name]['tables'][table_name]['create_sql'] != first_structure:
                        structure_identical = False
                        break
                
                if structure_identical:
                    total_records = sum(
                        self.analysis_results[db]['tables'][table_name]['record_count'] 
                        for db in db_list
                    )
                    merge_strategy['direct_merge'].append({
                        'table': table_name,
                        'databases': db_list,
                        'total_records': total_records,
                        'structure': first_structure
                    })
                else:
                    merge_strategy['manual_merge'].append({
                        'table': table_name,
                        'databases': db_list,
                        'structures': {
                            db: self.analysis_results[db]['tables'][table_name]['create_sql']
                            for db in db_list
                        }
                    })
        
        # 打印策略
        print("1. 可以直接合并的表:")
        for item in merge_strategy['direct_merge']:
            print(f"   - {item['table']}: {item['total_records']} 条记录 (来自 {len(item['databases'])} 个数据库)")
        
        print("\n2. 需要手动处理的表:")
        for item in merge_strategy['manual_merge']:
            print(f"   - {item['table']}: 结构不同，来自 {len(item['databases'])} 个数据库")
        
        print("\n3. 唯一表 (只在一个数据库中存在):")
        for item in merge_strategy['unique_tables']:
            print(f"   - {item['table']}: {item['records']} 条记录 (来自 {item['database']})")
        
        return merge_strategy
    
    def save_analysis_report(self):
        """保存分析报告"""
        report = {
            'analysis_time': datetime.now().isoformat(),
            'database_files': [str(f) for f in self.db_files],
            'analysis_results': self.analysis_results,
            'merge_strategy': self.generate_merge_strategy()
        }
        
        report_file = self.directory_path / 'database_analysis_report.json'
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        print(f"\n分析报告已保存到: {report_file}")
        return report

def main():
    # 分析当前目录
    analyzer = DatabaseAnalyzer('.')
    
    # 查找数据库文件
    analyzer.find_database_files()
    
    # 分析所有数据库
    analyzer.analyze_all_databases()
    
    # 比较表结构
    analyzer.compare_table_structures()
    
    # 生成合并策略
    analyzer.generate_merge_strategy()
    
    # 保存报告
    analyzer.save_analysis_report()

if __name__ == "__main__":
    main()
